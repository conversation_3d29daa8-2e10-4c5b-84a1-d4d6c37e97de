<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.StroopTestActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_light_blue"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Quit Button -->
        <ImageButton
            android:id="@+id/btn_quit"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="Quit game"
            app:tint="@color/text_white" />

        <!-- Game Title -->
        <TextView
            android:id="@+id/game_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Stroop Test"
            android:textColor="@color/text_white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_menu"
            android:contentDescription="Game menu"
            app:tint="@color/text_white" />

    </LinearLayout>

    <!-- Game Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="2dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Level -->
        <TextView
            android:id="@+id/level_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Level 1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Round -->
        <TextView
            android:id="@+id/round_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Round 1/3"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Trial -->
        <TextView
            android:id="@+id/trial_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Trial 1/10"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="16dp"
        android:text="Click the color of the INK, not the word!"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- Word Display Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:background="@color/surface_white"
        android:padding="24dp">

        <!-- Color Word Display -->
        <TextView
            android:id="@+id/word_display"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/stroop_word_background"
            android:gravity="center"
            android:text="RED"
            android:textSize="48sp"
            android:textStyle="bold"
            android:visibility="invisible"
            tools:text="BLUE"
            tools:textColor="@color/stroop_red" />

    </FrameLayout>

    <!-- Color Answer Buttons -->
    <LinearLayout
        android:id="@+id/color_buttons_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center">

        <!-- First Row: Red, Blue -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <Button
                android:id="@+id/btn_red"
                android:layout_width="140dp"
                android:layout_height="100dp"
                android:layout_margin="12dp"
                android:text="RED"
                android:textColor="@color/text_white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:backgroundTint="@color/stroop_red"
                app:cornerRadius="16dp"
                android:elevation="6dp" />

            <Button
                android:id="@+id/btn_blue"
                android:layout_width="140dp"
                android:layout_height="100dp"
                android:layout_margin="12dp"
                android:text="BLUE"
                android:textColor="@color/text_white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:backgroundTint="@color/stroop_blue"
                app:cornerRadius="16dp"
                android:elevation="6dp" />

        </LinearLayout>

        <!-- Second Row: Green, Yellow -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_green"
                android:layout_width="140dp"
                android:layout_height="100dp"
                android:layout_margin="12dp"
                android:text="GREEN"
                android:textColor="@color/text_white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:backgroundTint="@color/stroop_green"
                app:cornerRadius="16dp"
                android:elevation="6dp" />

            <Button
                android:id="@+id/btn_yellow"
                android:layout_width="140dp"
                android:layout_height="100dp"
                android:layout_margin="12dp"
                android:text="YELLOW"
                android:textColor="@color/text_primary"
                android:textSize="20sp"
                android:textStyle="bold"
                android:backgroundTint="@color/stroop_yellow"
                app:cornerRadius="16dp"
                android:elevation="6dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
