#Wed Jul 02 04:56:34 PDT 2025
com.leapiq.braintraining.app-main-5\:/anim/fade_in.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fade_in.xml
com.leapiq.braintraining.app-main-5\:/anim/fade_out.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fade_out.xml
com.leapiq.braintraining.app-main-5\:/drawable/anagram_input_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\anagram_input_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/anagram_scrambled_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\anagram_scrambled_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/button_danger.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_danger.xml
com.leapiq.braintraining.app-main-5\:/drawable/button_primary.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_primary.xml
com.leapiq.braintraining.app-main-5\:/drawable/button_primary_rounded.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_primary_rounded.xml
com.leapiq.braintraining.app-main-5\:/drawable/button_secondary.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_secondary.xml
com.leapiq.braintraining.app-main-5\:/drawable/card_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/circle_primary.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_primary.xml
com.leapiq.braintraining.app-main-5\:/drawable/dialog_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/estimation_answer_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\estimation_answer_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/estimation_display_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\estimation_display_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/focus_stimulus_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\focus_stimulus_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/game_card_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\game_card_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/gradient_learning_style.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_learning_style.xml
com.leapiq.braintraining.app-main-5\:/drawable/gradient_problem_solving.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_problem_solving.xml
com.leapiq.braintraining.app-main-5\:/drawable/gradient_stress_response.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_stress_response.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_disk_blue.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_disk_blue.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_disk_cyan.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_disk_cyan.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_disk_green.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_disk_green.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_disk_orange.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_disk_orange.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_disk_pink.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_disk_pink.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_disk_purple.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_disk_purple.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_disk_red.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_disk_red.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_disk_yellow.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_disk_yellow.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_tower_base.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_tower_base.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_tower_normal_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_tower_normal_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_tower_peg.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_tower_peg.xml
com.leapiq.braintraining.app-main-5\:/drawable/hanoi_tower_selected_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hanoi_tower_selected_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/header_icon_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\header_icon_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_analytics.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_analytics.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_apple.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_apple.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_arrow_back.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_back.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_car.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_car.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_card_back.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_card_back.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_check.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_circle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_circle.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_close.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_diamond.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_diamond.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_favorite.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_favorite.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_games.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_games.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_heart.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_heart.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_home.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_home.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_launcher_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_learning.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_learning.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_lock.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_lock.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_menu.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_menu.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_music.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_music.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_person.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_person.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_pets.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_pets.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_problem_solving.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_problem_solving.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_progress.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_progress.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_school.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_school.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_settings.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_sports.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_sports.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_square.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_square.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_star.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_star.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_streak.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_streak.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_stress.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_stress.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_test_placeholder.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_test_placeholder.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_tests.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_tests.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_timer.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_timer.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_today.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_today.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_triangle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_triangle.xml
com.leapiq.braintraining.app-main-5\:/drawable/ic_trophy.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_trophy.xml
com.leapiq.braintraining.app-main-5\:/drawable/logic_premises_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logic_premises_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/logic_question_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logic_question_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/logical_reasoning.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logical_reasoning.xml
com.leapiq.braintraining.app-main-5\:/drawable/math_answer_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\math_answer_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/math_problem_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\math_problem_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/memory_grid_default.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\memory_grid_default.xml
com.leapiq.braintraining.app-main-5\:/drawable/memory_grid_highlighted.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\memory_grid_highlighted.xml
com.leapiq.braintraining.app-main-5\:/drawable/memory_grid_pattern.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\memory_grid_pattern.xml
com.leapiq.braintraining.app-main-5\:/drawable/memory_grid_selected.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\memory_grid_selected.xml
com.leapiq.braintraining.app-main-5\:/drawable/number_display_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\number_display_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/number_input_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\number_input_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/pattern_cell_empty.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\pattern_cell_empty.xml
com.leapiq.braintraining.app-main-5\:/drawable/pattern_cell_filled.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\pattern_cell_filled.xml
com.leapiq.braintraining.app-main-5\:/drawable/pattern_display_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\pattern_display_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/pattern_question_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\pattern_question_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/premium_button_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\premium_button_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/reaction_area_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\reaction_area_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/reaction_pulse_effect.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\reaction_pulse_effect.xml
com.leapiq.braintraining.app-main-5\:/drawable/reaction_stimulus_circle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\reaction_stimulus_circle.xml
com.leapiq.braintraining.app-main-5\:/drawable/reaction_stimulus_dynamic.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\reaction_stimulus_dynamic.xml
com.leapiq.braintraining.app-main-5\:/drawable/rounded_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/rounded_background_light.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_background_light.xml
com.leapiq.braintraining.app-main-5\:/drawable/search_shape_circle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\search_shape_circle.xml
com.leapiq.braintraining.app-main-5\:/drawable/search_shape_diamond.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\search_shape_diamond.xml
com.leapiq.braintraining.app-main-5\:/drawable/search_shape_square.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\search_shape_square.xml
com.leapiq.braintraining.app-main-5\:/drawable/search_shape_triangle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\search_shape_triangle.xml
com.leapiq.braintraining.app-main-5\:/drawable/sequence_answer_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\sequence_answer_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/sequence_button_blue.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\sequence_button_blue.xml
com.leapiq.braintraining.app-main-5\:/drawable/sequence_button_green.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\sequence_button_green.xml
com.leapiq.braintraining.app-main-5\:/drawable/sequence_button_red.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\sequence_button_red.xml
com.leapiq.braintraining.app-main-5\:/drawable/sequence_button_yellow.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\sequence_button_yellow.xml
com.leapiq.braintraining.app-main-5\:/drawable/sequence_display_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\sequence_display_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/spatial_rotation.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spatial_rotation.xml
com.leapiq.braintraining.app-main-5\:/drawable/spatial_shape_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spatial_shape_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/speed_math_answer_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\speed_math_answer_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/speed_math_problem_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\speed_math_problem_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/splash_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\splash_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/stroop_word_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\stroop_word_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/tower_of_hanoi.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tower_of_hanoi.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_ball_blue.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_ball_blue.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_ball_cyan.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_ball_cyan.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_ball_green.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_ball_green.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_ball_orange.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_ball_orange.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_ball_pink.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_ball_pink.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_ball_purple.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_ball_purple.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_ball_red.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_ball_red.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_ball_yellow.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_ball_yellow.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_empty_slot.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_empty_slot.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_normal_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_normal_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/tube_selected_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tube_selected_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/vocabulary_prompt_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\vocabulary_prompt_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/word_search_cell_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\word_search_cell_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/word_search_grid_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\word_search_grid_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/word_search_list_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\word_search_list_background.xml
com.leapiq.braintraining.app-main-5\:/drawable/word_target_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\word_target_background.xml
com.leapiq.braintraining.app-main-5\:/menu/bottom_nav_menu.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_nav_menu.xml
com.leapiq.braintraining.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.leapiq.braintraining.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.leapiq.braintraining.app-main-5\:/mipmap-hdpi/ic_launcher.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.leapiq.braintraining.app-main-5\:/mipmap-hdpi/ic_launcher_foreground.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_foreground.webp
com.leapiq.braintraining.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.leapiq.braintraining.app-main-5\:/mipmap-mdpi/ic_launcher.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.leapiq.braintraining.app-main-5\:/mipmap-mdpi/ic_launcher_foreground.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_foreground.webp
com.leapiq.braintraining.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.leapiq.braintraining.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.leapiq.braintraining.app-main-5\:/mipmap-xhdpi/ic_launcher_foreground.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_foreground.webp
com.leapiq.braintraining.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.leapiq.braintraining.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.leapiq.braintraining.app-main-5\:/mipmap-xxhdpi/ic_launcher_foreground.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_foreground.webp
com.leapiq.braintraining.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.leapiq.braintraining.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.leapiq.braintraining.app-main-5\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_foreground.webp
com.leapiq.braintraining.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.leapiq.braintraining.app-main-5\:/navigation/nav_graph.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\navigation\\nav_graph.xml
com.leapiq.braintraining.app-main-5\:/xml/backup_rules.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.leapiq.braintraining.app-main-5\:/xml/data_extraction_rules.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_all_results.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_all_results.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_anagrams.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_anagrams.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_analytics.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_analytics.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_attention_test.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_attention_test.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_card_matching.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_card_matching.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_estimation.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_estimation.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_focus_challenge.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_focus_challenge.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_game_result.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_game_result.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_learning_style.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_learning_style.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_logic_puzzles.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_logic_puzzles.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_logical_reasoning.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_logical_reasoning.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_main.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_memory_assessment.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_memory_assessment.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_mental_arithmetic.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_mental_arithmetic.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_my_results.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_my_results.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_number_memory.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_number_memory.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_number_sequences.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_number_sequences.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_pattern_completion.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_pattern_completion.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_pattern_memory.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_pattern_memory.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_problem_solving_style.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_problem_solving_style.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_processing_speed.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_processing_speed.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_reaction_time.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_reaction_time.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_sequence_recall.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_sequence_recall.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_spatial_rotation.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_spatial_rotation.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_speed_math.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_speed_math.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_splash.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_splash.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_stress_response.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_stress_response.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_stroop_test.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_stroop_test.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_test_info.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_test_info.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_test_progress.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_test_progress.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_test_question_base.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_test_question_base.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_test_results.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_test_results.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_tower_of_hanoi.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_tower_of_hanoi.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_tube_sort.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_tube_sort.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_visual_search.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_visual_search.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_vocabulary.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_vocabulary.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_word_association.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_word_association.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/activity_word_search.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_word_search.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/dialog_game_menu.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_game_menu.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/fragment_games.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_games.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/fragment_profile.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_profile.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/fragment_progress.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_progress.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/fragment_tests.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_tests.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/fragment_today.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_today.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_achievement.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_achievement.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_all_results.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_all_results.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_category_accuracy.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_category_accuracy.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_daily_challenge.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_daily_challenge.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_detailed_score.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_detailed_score.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_game_card.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_game_card.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_hanoi_tower.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_hanoi_tower.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_key_point.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_key_point.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_learning_style_option.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_learning_style_option.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_logic_clue.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_logic_clue.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_logic_grid_cell.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_logic_grid_cell.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_logic_grid_header.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_logic_grid_header.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_logic_grid_row.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_logic_grid_row.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_memory_card.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_memory_card.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_memory_grid.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_memory_grid.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_pattern_cell.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_pattern_cell.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_problem_solving_option.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_problem_solving_option.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_recent_activity.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_recent_activity.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_search_shape.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_search_shape.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_stress_response_option.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_stress_response_option.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_test_card.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_test_card.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_test_instruction.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_test_instruction.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_test_progress.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_test_progress.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_test_result.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_test_result.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_tube.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_tube.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_word_search_cell.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_word_search_cell.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/item_word_search_word.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_word_search_word.xml
com.leapiq.braintraining.app-packageDebugResources-2\:/layout/layout_header.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_header.xml
