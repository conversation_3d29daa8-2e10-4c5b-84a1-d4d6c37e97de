package com.leapiq.braintraining.ui.tests

import com.leapiq.braintraining.R

/**
 * Information page for Problem Solving Style Test
 * Shows test description, instructions, and start button
 */
class ProblemSolvingStyleInfoActivity : BaseTestInfoActivity() {
    
    override fun loadTestInfo() {
        setTestInfo(
            title = "Problem Solving Style",
            subtitle = "Personality Test",
            description = "Discover your approach to problem-solving and decision-making. Learn whether you tend to be more analytical and systematic or intuitive and creative in your thinking process.",
            instructions = listOf(
                "Consider how you approach problems and make decisions",
                "Choose the option that best reflects your natural thinking style",
                "Think about your typical approach, not what you think is 'correct'",
                "Navigate freely between questions to review and modify answers",
                "Take your time to provide thoughtful responses"
            ),
            estimatedMinutes = 12,
            iconResource = R.drawable.ic_problem_solving
        )
    }
    
    override fun getTestActivityClass(): Class<*> {
        return ProblemSolvingStyleActivity::class.java
    }
    
    override fun getHeroGradient(): Int {
        return R.drawable.gradient_problem_solving
    }
}
