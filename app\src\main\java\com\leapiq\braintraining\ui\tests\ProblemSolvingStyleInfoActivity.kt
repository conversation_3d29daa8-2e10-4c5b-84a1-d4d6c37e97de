package com.leapiq.braintraining.ui.tests

import com.leapiq.braintraining.R

/**
 * Information page for Problem Solving Style Test
 * Shows test description, instructions, and start button
 */
class ProblemSolvingStyleInfoActivity : BaseTestInfoActivity() {
    
    override fun loadTestInfo() {
        setTestInfo(
            title = "Problem Solving Style",
            subtitle = "Personality Test",
            description = "Discover your approach to problem-solving and decision-making. Learn whether you tend to be more analytical and systematic or intuitive and creative in your thinking process.",
            instructions = listOf(
                "You'll face various problem-solving scenarios and puzzles",
                "Choose the approach that best represents how you would naturally handle each situation",
                "Consider how you approach problems and make decisions in real life",
                "Think about your typical approach, not what you think is 'correct'",
                "Some questions present real-world scenarios, others are logic puzzles",
                "Navigate freely between questions using Previous/Next buttons",
                "You can review and modify your answers before finishing",
                "Take your time to provide thoughtful, honest responses"
            ),
            estimatedMinutes = 12,
            iconResource = R.drawable.ic_problem_solving
        )
    }
    
    override fun getTestActivityClass(): Class<*> {
        return ProblemSolvingStyleActivity::class.java
    }
    
    override fun getHeroGradient(): Int {
        return R.drawable.gradient_problem_solving
    }
}
