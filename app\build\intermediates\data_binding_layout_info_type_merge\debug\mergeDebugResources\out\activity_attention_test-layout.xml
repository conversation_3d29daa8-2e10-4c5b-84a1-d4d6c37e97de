<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_attention_test" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_attention_test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_attention_test_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="223" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/progress_text" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="42"/></Target><Target id="@+id/task_type_text" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="50" endOffset="41"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="54" startOffset="8" endLine="60" endOffset="62"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="65" startOffset="4" endLine="75" endOffset="32"/></Target><Target id="@+id/stimulus_display" view="TextView"><Expressions/><location startLine="92" startOffset="12" endLine="102" endOffset="52"/></Target><Target id="@+id/feedback_text" view="TextView"><Expressions/><location startLine="105" startOffset="12" endLine="113" endOffset="43"/></Target><Target id="@+id/distractor_1" view="TextView"><Expressions/><location startLine="119" startOffset="8" endLine="129" endOffset="39"/></Target><Target id="@+id/distractor_2" view="TextView"><Expressions/><location startLine="132" startOffset="8" endLine="142" endOffset="39"/></Target><Target id="@+id/distractor_3" view="TextView"><Expressions/><location startLine="145" startOffset="8" endLine="155" endOffset="39"/></Target><Target id="@+id/distractor_4" view="TextView"><Expressions/><location startLine="158" startOffset="8" endLine="168" endOffset="39"/></Target><Target id="@+id/response_button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="181" startOffset="8" endLine="191" endOffset="37"/></Target></Targets></Layout>