<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_white"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="Attention Test"
        app:titleTextColor="@color/text_primary" />

    <!-- Progress Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/surface_white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/progress_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Trial 1 of 30"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/task_type_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Sustained Attention"
                android:textColor="@color/primary_light_blue"
                android:textSize="14sp" />

        </LinearLayout>

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="8dp"
            android:progressTint="@color/primary_light_blue" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="Press the button when you see the target letter. Stay focused throughout the test."
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:gravity="center"
        android:background="@drawable/rounded_background"
        android:padding="16dp" />

    <!-- Main Test Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="16dp">

        <!-- Central Stimulus Display -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center">

            <!-- Stimulus Display -->
            <TextView
                android:id="@+id/stimulus_display"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:gravity="center"
                android:text="+"
                android:textColor="@color/text_primary"
                android:textSize="120sp"
                android:textStyle="bold"
                android:background="@drawable/rounded_background"
                android:layout_marginBottom="32dp" />

            <!-- Feedback Text -->
            <TextView
                android:id="@+id/feedback_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textColor="@color/success_green"
                android:textSize="18sp"
                android:textStyle="bold"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Distractor Elements -->
        <!-- Top Left Distractor -->
        <TextView
            android:id="@+id/distractor_1"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="top|start"
            android:layout_margin="32dp"
            android:gravity="center"
            android:text="●"
            android:textSize="24sp"
            android:textColor="@color/stroop_red"
            android:visibility="gone" />

        <!-- Top Right Distractor -->
        <TextView
            android:id="@+id/distractor_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="top|end"
            android:layout_margin="32dp"
            android:gravity="center"
            android:text="■"
            android:textSize="24sp"
            android:textColor="@color/stroop_blue"
            android:visibility="gone" />

        <!-- Bottom Left Distractor -->
        <TextView
            android:id="@+id/distractor_3"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="bottom|start"
            android:layout_margin="32dp"
            android:gravity="center"
            android:text="▲"
            android:textSize="24sp"
            android:textColor="@color/stroop_green"
            android:visibility="gone" />

        <!-- Bottom Right Distractor -->
        <TextView
            android:id="@+id/distractor_4"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="bottom|end"
            android:layout_margin="32dp"
            android:gravity="center"
            android:text="♦"
            android:textSize="24sp"
            android:textColor="@color/stroop_yellow"
            android:visibility="gone" />

    </FrameLayout>

    <!-- Response Button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/surface_white"
        android:elevation="4dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/response_button"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:text="RESPOND"
            android:textColor="@color/surface_white"
            android:textSize="24sp"
            android:textStyle="bold"
            app:backgroundTint="@color/primary_light_blue"
            app:cornerRadius="12dp"
            android:enabled="false" />

        <!-- Target Information -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Target: "
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="X"
                android:textColor="@color/primary_light_blue"
                android:textSize="18sp"
                android:textStyle="bold"
                android:background="@drawable/rounded_background"
                android:padding="8dp"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
