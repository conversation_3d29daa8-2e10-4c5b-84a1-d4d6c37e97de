package com.leapiq.braintraining.ui.tests

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.leapiq.braintraining.databinding.ActivityMemoryAssessmentBinding
import com.leapiq.braintraining.data.model.TestType
import com.leapiq.braintraining.ui.tests.adapters.MemoryGridAdapter
import kotlin.random.Random

/**
 * Comprehensive Memory Assessment Test
 * Tests working memory, short-term memory, and long-term memory
 * Dynamic timing based on performance and task complexity
 */
class MemoryAssessmentActivity : BaseTestActivity() {
    
    private lateinit var binding: ActivityMemoryAssessmentBinding
    private lateinit var memoryGridAdapter: MemoryGridAdapter
    
    // Test configuration
    private var currentTaskType = MemoryTaskType.DIGIT_SPAN
    private var currentDifficulty = 1
    private var tasksCompleted = 0
    private val totalTasks = 20 // Dynamic - can increase based on performance
    
    // Current task data
    private var currentSequence = mutableListOf<Int>()
    private var currentPattern = mutableListOf<Int>()
    private var userResponse = mutableListOf<Int>()
    private var isShowingStimulus = false
    
    // Scoring data
    private var workingMemoryScore = 0.0
    private var shortTermMemoryScore = 0.0
    private var longTermMemoryScore = 0.0
    private var spatialMemoryScore = 0.0
    
    // Task types for comprehensive memory assessment
    private enum class MemoryTaskType {
        DIGIT_SPAN,        // Working memory - remember digit sequences
        SPATIAL_SPAN,      // Spatial working memory - remember locations
        WORD_LIST,         // Short-term memory - remember word lists
        PATTERN_RECALL,    // Visual pattern memory
        DELAYED_RECALL     // Long-term memory - recall earlier items
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMemoryAssessmentBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }
    
    override fun initializeTest() {
        totalQuestions = totalTasks
        setupUI()
        setupMemoryGrid()
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            
            // Setup progress
            progressBar.max = totalQuestions
            
            // Setup response buttons
            btnSubmitResponse.setOnClickListener { submitResponse() }
            btnClearResponse.setOnClickListener { clearResponse() }
            
            // Setup number pad for digit span tasks
            setupNumberPad()
        }
    }
    
    private fun setupNumberPad() {
        binding.apply {
            btn0.setOnClickListener { addDigit(0) }
            btn1.setOnClickListener { addDigit(1) }
            btn2.setOnClickListener { addDigit(2) }
            btn3.setOnClickListener { addDigit(3) }
            btn4.setOnClickListener { addDigit(4) }
            btn5.setOnClickListener { addDigit(5) }
            btn6.setOnClickListener { addDigit(6) }
            btn7.setOnClickListener { addDigit(7) }
            btn8.setOnClickListener { addDigit(8) }
            btn9.setOnClickListener { addDigit(9) }
        }
    }
    
    private fun setupMemoryGrid() {
        memoryGridAdapter = MemoryGridAdapter { position ->
            onGridItemClicked(position)
        }
        
        binding.memoryGrid.apply {
            layoutManager = GridLayoutManager(this@MemoryAssessmentActivity, 4)
            adapter = memoryGridAdapter
        }
    }
    
    override fun startQuestion() {
        updateProgress()
        selectNextTaskType()
        generateMemoryTask()
    }
    
    private fun updateProgress() {
        binding.apply {
            progressBar.progress = currentQuestion - 1
            progressText.text = "Task ${currentQuestion} of ${totalQuestions}"
            taskTypeText.text = getTaskTypeDescription(currentTaskType)
        }
    }
    
    private fun selectNextTaskType() {
        // Adaptive task selection based on performance and progress
        currentTaskType = when {
            currentQuestion <= 4 -> MemoryTaskType.DIGIT_SPAN
            currentQuestion <= 8 -> MemoryTaskType.SPATIAL_SPAN
            currentQuestion <= 12 -> MemoryTaskType.WORD_LIST
            currentQuestion <= 16 -> MemoryTaskType.PATTERN_RECALL
            else -> MemoryTaskType.DELAYED_RECALL
        }
        
        // Adjust difficulty based on recent performance
        adjustDifficulty()
    }
    
    private fun adjustDifficulty() {
        if (currentQuestion > 3) {
            val recentCorrect = questionResults.takeLast(3).count { it.isCorrect == true }
            currentDifficulty = when {
                recentCorrect >= 3 -> minOf(currentDifficulty + 1, 7)
                recentCorrect <= 1 -> maxOf(currentDifficulty - 1, 1)
                else -> currentDifficulty
            }
        }
    }
    
    private fun generateMemoryTask() {
        clearResponse()
        hideAllTaskViews()
        
        when (currentTaskType) {
            MemoryTaskType.DIGIT_SPAN -> generateDigitSpanTask()
            MemoryTaskType.SPATIAL_SPAN -> generateSpatialSpanTask()
            MemoryTaskType.WORD_LIST -> generateWordListTask()
            MemoryTaskType.PATTERN_RECALL -> generatePatternRecallTask()
            MemoryTaskType.DELAYED_RECALL -> generateDelayedRecallTask()
        }
    }
    
    private fun generateDigitSpanTask() {
        binding.digitSpanContainer.visibility = View.VISIBLE
        binding.instructionText.text = "Remember the sequence of numbers"
        
        val sequenceLength = 3 + currentDifficulty
        currentSequence.clear()
        repeat(sequenceLength) {
            currentSequence.add(Random.nextInt(0, 10))
        }
        
        showDigitSequence()
    }
    
    private fun showDigitSequence() {
        isShowingStimulus = true
        binding.digitDisplay.visibility = View.VISIBLE
        binding.numberPadContainer.visibility = View.GONE
        
        var index = 0
        val handler = Handler(Looper.getMainLooper())
        
        fun showNextDigit() {
            if (index < currentSequence.size) {
                binding.digitDisplay.text = currentSequence[index].toString()
                index++
                handler.postDelayed({ showNextDigit() }, 1000) // 1 second per digit
            } else {
                // Sequence complete, show response interface
                binding.digitDisplay.visibility = View.GONE
                binding.numberPadContainer.visibility = View.VISIBLE
                binding.instructionText.text = "Enter the sequence in order"
                isShowingStimulus = false
            }
        }
        
        showNextDigit()
    }
    
    private fun generateSpatialSpanTask() {
        binding.spatialSpanContainer.visibility = View.VISIBLE
        binding.instructionText.text = "Remember the sequence of highlighted squares"
        
        val sequenceLength = 2 + currentDifficulty
        currentPattern.clear()
        repeat(sequenceLength) {
            currentPattern.add(Random.nextInt(0, 16)) // 4x4 grid
        }
        
        memoryGridAdapter.setupGrid(16)
        showSpatialSequence()
    }
    
    private fun showSpatialSequence() {
        isShowingStimulus = true
        var index = 0
        val handler = Handler(Looper.getMainLooper())
        
        fun highlightNext() {
            if (index < currentPattern.size) {
                memoryGridAdapter.highlightPosition(currentPattern[index])
                index++
                handler.postDelayed({
                    memoryGridAdapter.clearHighlight()
                    handler.postDelayed({ highlightNext() }, 300)
                }, 800)
            } else {
                // Sequence complete
                binding.instructionText.text = "Click the squares in the same order"
                isShowingStimulus = false
            }
        }
        
        highlightNext()
    }
    
    private fun generateWordListTask() {
        binding.wordListContainer.visibility = View.VISIBLE
        binding.instructionText.text = "Remember these words"
        
        val wordCount = 3 + currentDifficulty
        val words = getRandomWords(wordCount)
        
        binding.wordListDisplay.text = words.joinToString("\n")
        
        // Show words for calculated time
        val displayTime = wordCount * 2000L // 2 seconds per word
        Handler(Looper.getMainLooper()).postDelayed({
            binding.wordListDisplay.visibility = View.GONE
            binding.wordInputContainer.visibility = View.VISIBLE
            binding.instructionText.text = "Type the words you remember (one per line)"
        }, displayTime)
    }
    
    private fun generatePatternRecallTask() {
        binding.patternRecallContainer.visibility = View.VISIBLE
        binding.instructionText.text = "Study this pattern"
        
        val patternSize = 9 + currentDifficulty * 2 // 9-21 squares
        currentPattern.clear()
        repeat(patternSize / 3) {
            currentPattern.add(Random.nextInt(0, 25)) // 5x5 grid
        }
        
        memoryGridAdapter.setupGrid(25)
        memoryGridAdapter.showPattern(currentPattern)
        
        // Show pattern for calculated time
        val studyTime = (patternSize * 300L) + 2000L // 300ms per square + 2s base
        Handler(Looper.getMainLooper()).postDelayed({
            memoryGridAdapter.hidePattern()
            binding.instructionText.text = "Click on the squares that were highlighted"
        }, studyTime)
    }
    
    private fun generateDelayedRecallTask() {
        binding.delayedRecallContainer.visibility = View.VISIBLE
        binding.instructionText.text = "Recall items from earlier tasks"
        
        // Use items from previous tasks for delayed recall
        if (questionResults.size >= 3) {
            val previousTask = questionResults[questionResults.size - 3]
            binding.delayedRecallText.text = "What was the first item in task ${previousTask.questionNumber}?"
            // Implementation depends on storing previous task data
        }
    }
    
    private fun hideAllTaskViews() {
        binding.apply {
            digitSpanContainer.visibility = View.GONE
            spatialSpanContainer.visibility = View.GONE
            wordListContainer.visibility = View.GONE
            patternRecallContainer.visibility = View.GONE
            delayedRecallContainer.visibility = View.GONE
        }
    }
    
    private fun addDigit(digit: Int) {
        if (!isShowingStimulus) {
            userResponse.add(digit)
            updateResponseDisplay()
        }
    }
    
    private fun onGridItemClicked(position: Int) {
        if (!isShowingStimulus) {
            userResponse.add(position)
            memoryGridAdapter.selectPosition(position)
        }
    }
    
    private fun updateResponseDisplay() {
        binding.responseDisplay.text = userResponse.joinToString(" ")
    }
    
    private fun clearResponse() {
        userResponse.clear()
        updateResponseDisplay()
        memoryGridAdapter.clearSelections()
        binding.wordInput.setText("")
    }
    
    private fun submitResponse() {
        val isCorrect = checkResponse()
        val responseTime = System.currentTimeMillis() - questionStartTime
        
        // Update task-specific scores
        updateTaskScores(isCorrect)
        
        completeQuestion(
            isCorrect = isCorrect,
            selectedAnswer = userResponse.joinToString(","),
            correctAnswer = getCurrentCorrectAnswer(),
            responseTime = responseTime
        )
    }
    
    private fun checkResponse(): Boolean {
        return when (currentTaskType) {
            MemoryTaskType.DIGIT_SPAN -> userResponse == currentSequence
            MemoryTaskType.SPATIAL_SPAN -> userResponse == currentPattern
            MemoryTaskType.PATTERN_RECALL -> userResponse.toSet() == currentPattern.toSet()
            MemoryTaskType.WORD_LIST -> checkWordListResponse()
            MemoryTaskType.DELAYED_RECALL -> checkDelayedRecallResponse()
        }
    }
    
    private fun checkWordListResponse(): Boolean {
        val userWords = binding.wordInput.text.toString()
            .split("\n")
            .map { it.trim().lowercase() }
            .filter { it.isNotEmpty() }
        
        // Calculate partial credit based on correct words
        val correctWords = getCurrentWordList()
        val correctCount = userWords.count { it in correctWords }
        return correctCount >= (correctWords.size * 0.7) // 70% threshold
    }
    
    private fun checkDelayedRecallResponse(): Boolean {
        // Simplified implementation
        return true
    }
    
    private fun updateTaskScores(isCorrect: Boolean) {
        val score = if (isCorrect) 1.0 else 0.0
        
        when (currentTaskType) {
            MemoryTaskType.DIGIT_SPAN -> workingMemoryScore += score
            MemoryTaskType.SPATIAL_SPAN -> spatialMemoryScore += score
            MemoryTaskType.WORD_LIST -> shortTermMemoryScore += score
            MemoryTaskType.PATTERN_RECALL -> shortTermMemoryScore += score
            MemoryTaskType.DELAYED_RECALL -> longTermMemoryScore += score
        }
    }
    
    private fun getCurrentCorrectAnswer(): String {
        return when (currentTaskType) {
            MemoryTaskType.DIGIT_SPAN -> currentSequence.joinToString(",")
            MemoryTaskType.SPATIAL_SPAN -> currentPattern.joinToString(",")
            MemoryTaskType.PATTERN_RECALL -> currentPattern.joinToString(",")
            MemoryTaskType.WORD_LIST -> getCurrentWordList().joinToString(",")
            MemoryTaskType.DELAYED_RECALL -> "delayed_recall_answer"
        }
    }
    
    private fun getCurrentWordList(): List<String> {
        // Return the current word list - implementation depends on word storage
        return listOf("example", "words", "list")
    }
    
    private fun getRandomWords(count: Int): List<String> {
        val wordPool = listOf(
            "apple", "book", "chair", "door", "elephant", "flower", "guitar", "house",
            "island", "jacket", "kitchen", "lamp", "mountain", "notebook", "ocean",
            "pencil", "queen", "river", "sunset", "table", "umbrella", "village",
            "window", "yellow", "zebra"
        )
        return wordPool.shuffled().take(count)
    }
    
    private fun getTaskTypeDescription(taskType: MemoryTaskType): String {
        return when (taskType) {
            MemoryTaskType.DIGIT_SPAN -> "Working Memory - Digit Span"
            MemoryTaskType.SPATIAL_SPAN -> "Spatial Memory"
            MemoryTaskType.WORD_LIST -> "Short-term Memory - Word List"
            MemoryTaskType.PATTERN_RECALL -> "Visual Pattern Memory"
            MemoryTaskType.DELAYED_RECALL -> "Long-term Memory - Delayed Recall"
        }
    }
    
    override fun getTestType(): TestType = TestType.COGNITIVE
    
    override fun calculateScore(): Int {
        val totalTasks = questionResults.size
        val correctTasks = questionResults.count { it.isCorrect == true }
        return if (totalTasks > 0) (correctTasks * 100) / totalTasks else 0
    }
    
    override fun generateDetailedScores(): Map<String, Double> {
        val taskCounts = mapOf(
            "Working Memory" to 4,
            "Spatial Memory" to 4,
            "Short-term Memory" to 8,
            "Long-term Memory" to 4
        )
        
        return mapOf(
            "Working Memory" to (workingMemoryScore / taskCounts["Working Memory"]!! * 100),
            "Spatial Memory" to (spatialMemoryScore / taskCounts["Spatial Memory"]!! * 100),
            "Short-term Memory" to (shortTermMemoryScore / taskCounts["Short-term Memory"]!! * 100),
            "Long-term Memory" to (longTermMemoryScore / taskCounts["Long-term Memory"]!! * 100)
        )
    }
    
    override fun generateInsights(): List<String> {
        val insights = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        
        detailedScores.forEach { (type, score) ->
            when {
                score >= 80 -> insights.add("Excellent $type performance - you show strong abilities in this area")
                score >= 60 -> insights.add("Good $type performance - with practice, you can improve further")
                else -> insights.add("$type may benefit from targeted exercises and practice")
            }
        }
        
        return insights
    }
    
    override fun generateRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        
        if (detailedScores["Working Memory"]!! < 70) {
            recommendations.add("Practice dual n-back exercises to improve working memory capacity")
        }
        
        if (detailedScores["Spatial Memory"]!! < 70) {
            recommendations.add("Try spatial puzzles and mental rotation exercises")
        }
        
        if (detailedScores["Short-term Memory"]!! < 70) {
            recommendations.add("Use chunking techniques and repetition to enhance short-term memory")
        }
        
        recommendations.add("Regular sleep and exercise significantly improve memory performance")
        recommendations.add("Practice mindfulness meditation to enhance attention and memory")
        
        return recommendations
    }
    
    override fun showInstructions() {
        binding.instructionText.text = "You will complete various memory tasks. Follow the instructions for each task."
    }
}
