<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/ic_today"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_today"
        android:supportsRtl="true"
        android:theme="@style/Theme.LeapIQ"
        tools:targetApi="31">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.LeapIQ">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Game Activities -->
        <activity
            android:name=".ui.games.CardMatchingActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.SpeedMathActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.StroopTestActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.VocabularyActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.WordSearchActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <!-- Memory Games -->
        <activity
            android:name=".ui.games.SequenceRecallActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.PatternMemoryActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.NumberMemoryActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <!-- Attention Games -->
        <activity
            android:name=".ui.games.ReactionTimeActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.VisualSearchActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.FocusChallengeActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <!-- Math Games -->
        <activity
            android:name=".ui.games.MentalArithmeticActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.NumberSequencesActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.EstimationActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <!-- Logic Games -->
        <activity
            android:name=".ui.games.TubeSortActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.LogicalReasoningActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.SpatialRotationActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.TowerOfHanoiActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.PatternCompletionActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <!-- Language Games -->
        <activity
            android:name=".ui.games.WordAssociationActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.AnagramsActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.LogicPuzzlesActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.games.GameResultActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <!-- Test Activities -->


        <activity
            android:name=".ui.tests.LearningStyleActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.tests.ProblemSolvingStyleActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.tests.StressResponseActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.tests.results.TestResultsActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.tests.results.AllResultsActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.tests.results.MyResultsActivity"
            android:exported="false"
            android:theme="@style/Theme.LeapIQ"
            android:screenOrientation="portrait" />

    </application>

</manifest>
