<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_memory_assessment" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_memory_assessment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_memory_assessment_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="397" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/progress_text" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="42"/></Target><Target id="@+id/task_type_text" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="50" endOffset="41"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="54" startOffset="8" endLine="60" endOffset="62"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="77" startOffset="12" endLine="87" endOffset="40"/></Target><Target id="@+id/digit_span_container" view="LinearLayout"><Expressions/><location startLine="90" startOffset="12" endLine="268" endOffset="26"/></Target><Target id="@+id/digit_display" view="TextView"><Expressions/><location startLine="98" startOffset="16" endLine="108" endOffset="56"/></Target><Target id="@+id/response_display" view="TextView"><Expressions/><location startLine="111" startOffset="16" endLine="122" endOffset="46"/></Target><Target id="@+id/number_pad_container" view="LinearLayout"><Expressions/><location startLine="125" startOffset="16" endLine="266" endOffset="30"/></Target><Target id="@+id/btn_1" view="Button"><Expressions/><location startLine="138" startOffset="24" endLine="147" endOffset="69"/></Target><Target id="@+id/btn_2" view="Button"><Expressions/><location startLine="149" startOffset="24" endLine="158" endOffset="69"/></Target><Target id="@+id/btn_3" view="Button"><Expressions/><location startLine="160" startOffset="24" endLine="169" endOffset="69"/></Target><Target id="@+id/btn_4" view="Button"><Expressions/><location startLine="179" startOffset="24" endLine="188" endOffset="69"/></Target><Target id="@+id/btn_5" view="Button"><Expressions/><location startLine="190" startOffset="24" endLine="199" endOffset="69"/></Target><Target id="@+id/btn_6" view="Button"><Expressions/><location startLine="201" startOffset="24" endLine="210" endOffset="69"/></Target><Target id="@+id/btn_7" view="Button"><Expressions/><location startLine="220" startOffset="24" endLine="229" endOffset="69"/></Target><Target id="@+id/btn_8" view="Button"><Expressions/><location startLine="231" startOffset="24" endLine="240" endOffset="69"/></Target><Target id="@+id/btn_9" view="Button"><Expressions/><location startLine="242" startOffset="24" endLine="251" endOffset="69"/></Target><Target id="@+id/btn_0" view="Button"><Expressions/><location startLine="255" startOffset="20" endLine="264" endOffset="65"/></Target><Target id="@+id/spatial_span_container" view="LinearLayout"><Expressions/><location startLine="271" startOffset="12" endLine="284" endOffset="26"/></Target><Target id="@+id/memory_grid" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="278" startOffset="16" endLine="282" endOffset="56"/></Target><Target id="@+id/word_list_container" view="LinearLayout"><Expressions/><location startLine="287" startOffset="12" endLine="326" endOffset="26"/></Target><Target id="@+id/word_list_display" view="TextView"><Expressions/><location startLine="294" startOffset="16" endLine="304" endOffset="44"/></Target><Target id="@+id/word_input_container" view="LinearLayout"><Expressions/><location startLine="306" startOffset="16" endLine="324" endOffset="30"/></Target><Target id="@+id/word_input" view="EditText"><Expressions/><location startLine="313" startOffset="20" endLine="322" endOffset="48"/></Target><Target id="@+id/pattern_recall_container" view="LinearLayout"><Expressions/><location startLine="329" startOffset="12" endLine="338" endOffset="26"/></Target><Target id="@+id/delayed_recall_container" view="LinearLayout"><Expressions/><location startLine="341" startOffset="12" endLine="359" endOffset="26"/></Target><Target id="@+id/delayed_recall_text" view="TextView"><Expressions/><location startLine="348" startOffset="16" endLine="357" endOffset="44"/></Target><Target id="@+id/btn_clear_response" view="Button"><Expressions/><location startLine="374" startOffset="8" endLine="383" endOffset="53"/></Target><Target id="@+id/btn_submit_response" view="Button"><Expressions/><location startLine="385" startOffset="8" endLine="393" endOffset="60"/></Target></Targets></Layout>