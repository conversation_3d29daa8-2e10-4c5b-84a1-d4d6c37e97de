package com.leapiq.braintraining.ui.tests

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.FragmentTestsBinding
import com.leapiq.braintraining.ui.tests.adapter.TestAdapter
import com.leapiq.braintraining.data.model.Test
import com.leapiq.braintraining.data.model.TestCategory
import com.leapiq.braintraining.data.TestProgressManager
import com.leapiq.braintraining.ui.tests.results.MyResultsActivity
import com.leapiq.braintraining.ui.tests.MemoryAssessmentActivity
import com.leapiq.braintraining.ui.tests.AttentionTestActivity
import com.leapiq.braintraining.ui.tests.ProcessingSpeedActivity
import com.leapiq.braintraining.ui.tests.LearningStyleActivity
import com.leapiq.braintraining.ui.tests.StressResponseActivity
import com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity

class TestsFragment : Fragment() {

    private var _binding: FragmentTestsBinding? = null
    private val binding get() = _binding!!

    private lateinit var testAdapter: TestAdapter
    private lateinit var progressManager: TestProgressManager
    private var allTests: List<Test> = emptyList()
    private var currentCategory = TestCategory.ALL

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentTestsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        progressManager = TestProgressManager.getInstance(requireContext())

        setupHeader()
        setupTestsGrid()
        setupCategoryTabs()
        setupMyResultsButton()
        loadTests()
    }
    
    private fun setupHeader() {
        // Update header title to "Tests"
        binding.root.findViewById<android.widget.TextView>(R.id.header_title)?.text =
            getString(R.string.nav_tests)
    }

    private fun setupMyResultsButton() {
        binding.btnMyResults.setOnClickListener {
            val intent = Intent(requireContext(), MyResultsActivity::class.java)
            startActivity(intent)
        }

        // Update button appearance based on whether user has completed any tests
        updateMyResultsButton()
    }

    private fun updateMyResultsButton() {
        val hasCompletedTests = progressManager.getAllTestProgresses().values.any { it.timesCompleted > 0 }

        binding.btnMyResults.apply {
            if (hasCompletedTests) {
                text = "📊 My Results"
                alpha = 1.0f
                isEnabled = true
            } else {
                text = "📊 My Results (Complete a test first)"
                alpha = 0.6f
                isEnabled = false
            }
        }
    }

    private fun setupTestsGrid() {
        testAdapter = TestAdapter { test ->
            onTestClicked(test)
        }
        
        binding.testsRecycler.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = testAdapter
        }
    }
    
    private fun setupCategoryTabs() {
        binding.apply {
            tabAllTests.setOnClickListener { selectCategory(TestCategory.ALL) }
            tabCognitive.setOnClickListener { selectCategory(TestCategory.COGNITIVE) }
            tabPersonality.setOnClickListener { selectCategory(TestCategory.PERSONALITY) }
        }
        
        // Select "All" by default
        selectCategory(TestCategory.ALL)
    }
    
    private fun selectCategory(category: TestCategory) {
        currentCategory = category
        updateTabSelection()
        filterTests()
    }
    
    private fun updateTabSelection() {
        val primaryColor = resources.getColor(R.color.primary_light_blue, null)
        val secondaryColor = resources.getColor(R.color.text_secondary, null)
        
        binding.apply {
            tabAllTests.setTextColor(if (currentCategory == TestCategory.ALL) primaryColor else secondaryColor)
            tabCognitive.setTextColor(if (currentCategory == TestCategory.COGNITIVE) primaryColor else secondaryColor)
            tabPersonality.setTextColor(if (currentCategory == TestCategory.PERSONALITY) primaryColor else secondaryColor)
        }
    }
    
    private fun filterTests() {
        val filteredTests = if (currentCategory == TestCategory.ALL) {
            allTests
        } else {
            allTests.filter { it.category == currentCategory }
        }
        testAdapter.submitList(filteredTests)
    }
    
    private fun loadTests() {
        allTests = createTestsList()
        filterTests()
        updateMyResultsButton()
    }
    
    private fun createTestsList(): List<Test> {
        return listOf(
            // Cognitive Tests
            Test(
                id = "memory_assessment",
                name = getString(R.string.memory_assessment),
                category = TestCategory.COGNITIVE,
                description = "Comprehensive memory evaluation",
                imageResource = "ic_memory",
                isCompleted = false
            ),
            Test(
                id = "attention_test",
                name = getString(R.string.attention_test),
                category = TestCategory.COGNITIVE,
                description = "Sustained attention measurement",
                imageResource = "ic_attention",
                isCompleted = false
            ),
            Test(
                id = "processing_speed",
                name = getString(R.string.processing_speed),
                category = TestCategory.COGNITIVE,
                description = "Response time evaluation",
                imageResource = "ic_speed",
                isCompleted = false
            ),
            
            // Personality Tests
            Test(
                id = "learning_style",
                name = getString(R.string.learning_style),
                category = TestCategory.PERSONALITY,
                description = "Visual, auditory, kinesthetic preferences",
                imageResource = "ic_learning",
                isCompleted = false
            ),
            Test(
                id = "stress_response",
                name = getString(R.string.stress_response),
                category = TestCategory.PERSONALITY,
                description = "How you handle pressure situations",
                imageResource = "ic_stress",
                isCompleted = false
            ),
            Test(
                id = "problem_solving",
                name = getString(R.string.problem_solving_style),
                category = TestCategory.PERSONALITY,
                description = "Analytical vs intuitive approach",
                imageResource = "ic_problem",
                isCompleted = false
            )
        )
    }
    
    private fun onTestClicked(test: Test) {
        val intent = when (test.id) {
            // Cognitive Tests
            "memory_assessment" -> Intent(requireContext(), MemoryAssessmentActivity::class.java)
            "attention_test" -> Intent(requireContext(), AttentionTestActivity::class.java)
            "processing_speed" -> Intent(requireContext(), ProcessingSpeedActivity::class.java)

            // Personality Tests
            "learning_style" -> Intent(requireContext(), LearningStyleActivity::class.java)
            "stress_response" -> Intent(requireContext(), StressResponseActivity::class.java)
            "problem_solving" -> Intent(requireContext(), ProblemSolvingStyleActivity::class.java)

            else -> {
                // Show coming soon message for other tests
                showComingSoonMessage(test.name)
                return
            }
        }

        // Add the test ID to the intent - required by BaseTestActivity
        intent.putExtra("test_id", test.id)
        startActivity(intent)
    }

    private fun showComingSoonMessage(testTitle: String) {
        android.widget.Toast.makeText(
            requireContext(),
            "$testTitle is coming soon!",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
