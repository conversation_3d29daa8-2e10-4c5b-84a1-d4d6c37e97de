package com.leapiq.braintraining.ui.tests

import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.data.TestProgressManager
import com.leapiq.braintraining.data.model.TestResult
import com.leapiq.braintraining.data.model.TestQuestionResult
import com.leapiq.braintraining.data.model.TestType
import com.leapiq.braintraining.ui.tests.results.TestResultsActivity
import com.google.gson.Gson
import java.util.Date

/**
 * Base class for all cognitive and personality tests
 * Provides common functionality for timing, scoring, and progress tracking
 */
abstract class BaseTestActivity : AppCompatActivity() {
    
    protected lateinit var progressManager: TestProgressManager
    protected lateinit var testId: String
    protected var currentQuestion: Int = 1
    protected var totalQuestions: Int = 0

    // Timing
    protected var testStartTime: Long = 0
    protected var questionStartTime: Long = 0

    // Results tracking
    protected val questionResults = mutableListOf<TestQuestionResult>()

    // Navigation support
    protected val userAnswers = mutableMapOf<Int, Any>() // Store answers for back navigation
    protected var isNavigationEnabled = true
    
    companion object {
        const val EXTRA_TEST_ID = "test_id"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        progressManager = TestProgressManager.getInstance(this)

        // Get test parameters from intent
        testId = intent.getStringExtra(EXTRA_TEST_ID) ?: ""

        if (testId.isEmpty()) {
            finish()
            return
        }

        // 1. Let child initialize its UI first (binding setup)
        initializeUI()

        // 2. Then initialize test logic
        initializeTest()

        // 3. Finally start the test
        startTest()
    }

    /**
     * Initialize UI components and binding (called first)
     */
    protected abstract fun initializeUI()

    /**
     * Initialize test-specific logic (called after UI is ready)
     */
    protected abstract fun initializeTest()
    
    /**
     * Start the next question/task
     */
    protected abstract fun startQuestion()
    
    /**
     * Get the test type (cognitive or personality)
     */
    protected abstract fun getTestType(): TestType
    
    /**
     * Calculate the final score based on question results
     */
    protected abstract fun calculateScore(): Int
    
    /**
     * Generate detailed scores for multi-dimensional tests
     */
    protected abstract fun generateDetailedScores(): Map<String, Double>
    
    /**
     * Generate insights based on test results
     */
    protected abstract fun generateInsights(): List<String>
    
    /**
     * Generate recommendations based on test results
     */
    protected abstract fun generateRecommendations(): List<String>
    
    /**
     * Show test instructions
     */
    protected abstract fun showInstructions()
    
    /**
     * Start the test
     */
    protected fun startTest() {
        testStartTime = SystemClock.elapsedRealtime()
        currentQuestion = 1
        questionResults.clear()
        
        showInstructions()
        startQuestion()
    }
    
    /**
     * Start a new question
     */
    protected fun startNewQuestion() {
        questionStartTime = SystemClock.elapsedRealtime()
        startQuestion()
    }
    
    /**
     * Complete the current question with result
     */
    protected fun completeQuestion(
        isCorrect: Boolean? = null,
        selectedAnswer: String? = null,
        correctAnswer: String? = null,
        confidence: Int? = null,
        responseTime: Long? = null
    ) {
        val questionTime = responseTime ?: (SystemClock.elapsedRealtime() - questionStartTime)
        
        val questionResult = TestQuestionResult(
            questionNumber = currentQuestion,
            isCorrect = isCorrect,
            responseTime = questionTime,
            selectedAnswer = selectedAnswer,
            correctAnswer = correctAnswer,
            confidence = confidence
        )
        
        questionResults.add(questionResult)
        
        if (currentQuestion < totalQuestions) {
            currentQuestion++
            startNewQuestion()
        } else {
            completeTest()
        }
    }
    
    /**
     * Complete the test and calculate results
     */
    private fun completeTest() {
        val totalTime = SystemClock.elapsedRealtime() - testStartTime
        val score = calculateScore()
        val detailedScores = generateDetailedScores()
        val insights = generateInsights()
        val recommendations = generateRecommendations()
        
        val testResult = TestResult(
            testId = testId,
            testType = getTestType(),
            questions = questionResults.toList(),
            totalTimeMs = totalTime,
            score = score,
            completedAt = Date(),
            detailedScores = detailedScores,
            insights = insights,
            recommendations = recommendations
        )
        
        // Save progress
        progressManager.saveTestResult(testResult)
        
        // Show results
        showTestResults(testResult)
    }
    
    /**
     * Show the test results screen
     */
    private fun showTestResults(testResult: TestResult) {
        val intent = Intent(this, TestResultsActivity::class.java).apply {
            putExtra(TestResultsActivity.EXTRA_TEST_RESULT, Gson().toJson(testResult))
        }
        startActivity(intent)
        finish()
    }
    
    /**
     * Get progress for UI updates
     */
    protected fun getProgressPercentage(): Int {
        return if (totalQuestions > 0) {
            ((currentQuestion - 1) * 100) / totalQuestions
        } else 0
    }
    
    /**
     * Check if test should continue based on dynamic timing
     */
    protected fun shouldContinueTest(): Boolean {
        // Default implementation - can be overridden for dynamic timing
        return currentQuestion <= totalQuestions
    }
    
    /**
     * Adaptive question count based on performance
     */
    protected fun adjustQuestionCount(baseCount: Int): Int {
        // Default implementation - can be overridden for adaptive testing
        return baseCount
    }

    // ========== NAVIGATION METHODS ==========

    /**
     * Navigate to previous question
     */
    protected fun goToPreviousQuestion() {
        if (currentQuestion > 1) {
            currentQuestion--
            loadQuestion(currentQuestion)
            updateNavigationButtons()
            updateProgressDisplay()
        }
    }

    /**
     * Navigate to next question
     */
    protected fun goToNextQuestion() {
        if (currentQuestion < totalQuestions) {
            currentQuestion++
            loadQuestion(currentQuestion)
            updateNavigationButtons()
            updateProgressDisplay()
        } else {
            // Last question - finish test
            finishTest()
        }
    }

    /**
     * Save current answer for navigation support
     */
    protected fun saveCurrentAnswer(answer: Any) {
        userAnswers[currentQuestion] = answer
    }

    /**
     * Get saved answer for current question
     */
    protected fun getSavedAnswer(): Any? {
        return userAnswers[currentQuestion]
    }

    /**
     * Update navigation button states
     */
    protected open fun updateNavigationButtons() {
        // Override in child classes to update button visibility/state
    }

    /**
     * Update progress display
     */
    protected open fun updateProgressDisplay() {
        // Override in child classes to update progress indicators
    }

    /**
     * Load specific question (for navigation)
     */
    protected abstract fun loadQuestion(questionNumber: Int)

    /**
     * Check if current question has been answered
     */
    protected open fun isCurrentQuestionAnswered(): Boolean {
        return userAnswers.containsKey(currentQuestion)
    }

    /**
     * Finish the test and show results
     */
    protected open fun finishTest() {
        completeTest()
    }
}
