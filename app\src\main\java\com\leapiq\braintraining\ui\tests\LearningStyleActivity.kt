package com.leapiq.braintraining.ui.tests

import android.os.Bundle
import android.view.View
import android.widget.RadioButton
import android.widget.RadioGroup
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityTestQuestionBaseBinding
import com.leapiq.braintraining.data.model.TestType

// Learning style preferences
enum class LearningPreference {
    VISUAL,      // V - Visual learners
    AUDITORY,    // A - Auditory learners
    READ_WRITE,  // R - Read/Write learners
    KINESTHETIC  // K - Kinesthetic learners
}

// Question data class
data class LearningStyleQuestion(
    val id: Int,
    val scenario: String,
    val options: List<LearningStyleOption>
)

data class LearningStyleOption(
    val text: String,
    val preference: LearningPreference,
    val isSelected: Boolean = false
)

/**
 * Learning Style Test using VARK model
 * Tests Visual, Auditory, Read/Write, and Kinesthetic learning preferences
 * Dynamic timing based on question complexity and user reflection time
 */
class LearningStyleActivity : BaseTestActivity() {

    private lateinit var binding: ActivityTestQuestionBaseBinding
    private val questions = mutableListOf<LearningStyleQuestion>()

    // Scoring data
    private var visualScore = 0
    private var auditoryScore = 0
    private var readWriteScore = 0
    private var kinestheticScore = 0
    

    
    override fun initializeUI() {
        binding = ActivityTestQuestionBaseBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupUI()
    }

    override fun initializeTest() {
        generateQuestions()
        totalQuestions = questions.size
        loadQuestion(1) // Load first question
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            toolbar.title = "Learning Style Test"

            // Setup navigation buttons
            btnPrevious.setOnClickListener { goToPreviousQuestion() }
            btnNext.setOnClickListener {
                if (currentQuestion == totalQuestions) {
                    finishTest()
                } else {
                    goToNextQuestion()
                }
            }
        }
    }

    override fun loadQuestion(questionNumber: Int) {
        if (questionNumber <= questions.size) {
            val question = questions[questionNumber - 1]

            binding.apply {
                questionText.text = question.scenario
                progressText.text = "Question $questionNumber of $totalQuestions"
                progressBar.progress = ((questionNumber - 1) * 100) / totalQuestions

                // Clear previous content
                questionContent.removeAllViews()

                // Create radio group for options
                val radioGroup = RadioGroup(this@LearningStyleActivity)
                radioGroup.orientation = RadioGroup.VERTICAL

                question.options.forEachIndexed { index, option ->
                    val radioButton = RadioButton(this@LearningStyleActivity)
                    radioButton.text = option.text
                    radioButton.id = index
                    radioButton.textSize = 16f
                    radioButton.setPadding(16, 16, 16, 16)
                    radioButton.setTextColor(ContextCompat.getColor(this@LearningStyleActivity, R.color.text_primary))

                    // Restore saved answer
                    val savedAnswer = getSavedAnswer() as? Int
                    if (savedAnswer == index) {
                        radioButton.isChecked = true
                    }

                    radioGroup.addView(radioButton)
                }

                // Handle selection
                radioGroup.setOnCheckedChangeListener { _, checkedId ->
                    if (checkedId != -1) {
                        saveCurrentAnswer(checkedId)
                        updateNavigationButtons()
                    }
                }

                questionContent.addView(radioGroup)
                updateNavigationButtons()
            }
        }
    }

    override fun updateNavigationButtons() {
        binding.apply {
            // Show Previous button if not first question
            btnPrevious.visibility = if (currentQuestion > 1) View.VISIBLE else View.GONE

            // Update Next/Finish button
            val hasAnswer = isCurrentQuestionAnswered()
            btnNext.isEnabled = hasAnswer
            btnNext.text = if (currentQuestion == totalQuestions) "Finish" else "Next"
        }
    }

    override fun updateProgressDisplay() {
        binding.apply {
            progressText.text = "Question $currentQuestion of $totalQuestions"
            progressBar.progress = ((currentQuestion - 1) * 100) / totalQuestions
        }
    }

    override fun finishTest() {
        // Calculate scores from all answers
        calculateFinalScores()
        completeTest()
    }

    private fun calculateFinalScores() {
        visualScore = 0
        auditoryScore = 0
        readWriteScore = 0
        kinestheticScore = 0

        userAnswers.forEach { (questionIndex, answerIndex) ->
            if (questionIndex <= questions.size && answerIndex is Int) {
                val question = questions[questionIndex - 1]
                if (answerIndex < question.options.size) {
                    val selectedOption = question.options[answerIndex]
                    when (selectedOption.preference) {
                        LearningPreference.VISUAL -> visualScore++
                        LearningPreference.AUDITORY -> auditoryScore++
                        LearningPreference.READ_WRITE -> readWriteScore++
                        LearningPreference.KINESTHETIC -> kinestheticScore++
                    }
                }
            }
        }
    }

    private fun generateQuestions() {
        questions.addAll(listOf(
            LearningStyleQuestion(
                1,
                "When learning from the Internet, I like:",
                listOf(
                    LearningStyleOption("Podcasts and videos where I can listen to experts", LearningPreference.AUDITORY),
                    LearningStyleOption("Interesting design and visual features", LearningPreference.VISUAL),
                    LearningStyleOption("Videos showing how to do things", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Detailed articles and written content", LearningPreference.READ_WRITE)
                )
            ),
            LearningStyleQuestion(
                2,
                "I have a medical problem and want to learn about it. I would:",
                listOf(
                    LearningStyleOption("Have a detailed discussion with my doctor", LearningPreference.AUDITORY),
                    LearningStyleOption("Read articles that explain the problem", LearningPreference.READ_WRITE),
                    LearningStyleOption("Use a 3D model to see what is wrong", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Look at diagrams showing what was wrong", LearningPreference.VISUAL)
                )
            ),
            LearningStyleQuestion(
                3,
                "When choosing a career or area of study, these are important for me:",
                listOf(
                    LearningStyleOption("Applying my knowledge in real situations", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Communicating with others through discussion", LearningPreference.AUDITORY),
                    LearningStyleOption("Working with designs, maps or charts", LearningPreference.VISUAL),
                    LearningStyleOption("Using words well in written communications", LearningPreference.READ_WRITE)
                )
            ),
            LearningStyleQuestion(
                4,
                "I want to learn how to play a new board game. I would:",
                listOf(
                    LearningStyleOption("Use diagrams that explain the various stages and strategies", LearningPreference.VISUAL),
                    LearningStyleOption("Listen to somebody explaining it and ask questions", LearningPreference.AUDITORY),
                    LearningStyleOption("Watch others play the game before joining in", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Read the instructions carefully", LearningPreference.READ_WRITE)
                )
            ),
            LearningStyleQuestion(
                5,
                "I have finished a test and want feedback. I would like:",
                listOf(
                    LearningStyleOption("Graphs showing how my performance has improved", LearningPreference.VISUAL),
                    LearningStyleOption("A written description of my results", LearningPreference.READ_WRITE),
                    LearningStyleOption("Someone who talks it through with me", LearningPreference.AUDITORY),
                    LearningStyleOption("Examples from what I have done", LearningPreference.KINESTHETIC)
                )
            ),
            LearningStyleQuestion(
                6,
                "I want to learn how to take better photos. I would:",
                listOf(
                    LearningStyleOption("Use examples of good and poor photos showing improvements", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Use written instructions about what to do", LearningPreference.READ_WRITE),
                    LearningStyleOption("Ask questions and talk about the camera features", LearningPreference.AUDITORY),
                    LearningStyleOption("Use diagrams showing the camera and what each part does", LearningPreference.VISUAL)
                )
            ),
            LearningStyleQuestion(
                7,
                "I am having trouble assembling furniture. I would:",
                listOf(
                    LearningStyleOption("Study diagrams showing each stage of assembly", LearningPreference.VISUAL),
                    LearningStyleOption("Read the instructions that came with the furniture", LearningPreference.READ_WRITE),
                    LearningStyleOption("Watch a video of someone assembling similar furniture", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Ask for advice from someone who assembles furniture", LearningPreference.AUDITORY)
                )
            ),
            LearningStyleQuestion(
                8,
                "I want to learn about a new project. I would ask for:",
                listOf(
                    LearningStyleOption("Examples where the project has been used successfully", LearningPreference.KINESTHETIC),
                    LearningStyleOption("An opportunity to discuss the project", LearningPreference.AUDITORY),
                    LearningStyleOption("A written report describing the main features", LearningPreference.READ_WRITE),
                    LearningStyleOption("Diagrams showing project stages with charts", LearningPreference.VISUAL)
                )
            ),
            LearningStyleQuestion(
                9,
                "A website has a video with speaking, text, and diagrams. I learn most from:",
                listOf(
                    LearningStyleOption("Watching the actions", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Seeing the diagrams", LearningPreference.VISUAL),
                    LearningStyleOption("Listening to the speaking", LearningPreference.AUDITORY),
                    LearningStyleOption("Reading the words", LearningPreference.READ_WRITE)
                )
            ),
            LearningStyleQuestion(
                10,
                "When finding my way to a new place, I:",
                listOf(
                    LearningStyleOption("Rely on verbal directions from GPS or others", LearningPreference.AUDITORY),
                    LearningStyleOption("Like to read written instructions", LearningPreference.READ_WRITE),
                    LearningStyleOption("Rely on maps or GPS visual displays", LearningPreference.VISUAL),
                    LearningStyleOption("Head in the general direction and find my way", LearningPreference.KINESTHETIC)
                )
            ),
            LearningStyleQuestion(
                11,
                "I want to find out about a tour I'm going on. I would:",
                listOf(
                    LearningStyleOption("Read about the tour on the itinerary", LearningPreference.READ_WRITE),
                    LearningStyleOption("Talk with the person who planned the tour", LearningPreference.AUDITORY),
                    LearningStyleOption("Use a map and see where the places are", LearningPreference.VISUAL),
                    LearningStyleOption("Look at details about highlights and activities", LearningPreference.KINESTHETIC)
                )
            ),
            LearningStyleQuestion(
                12,
                "I prefer a teacher who uses:",
                listOf(
                    LearningStyleOption("Demonstrations, models or practical sessions", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Question and answer, talk, group discussion", LearningPreference.AUDITORY),
                    LearningStyleOption("Diagrams, charts, maps or graphs", LearningPreference.VISUAL),
                    LearningStyleOption("Handouts, books, or readings", LearningPreference.READ_WRITE)
                )
            ),
            LearningStyleQuestion(
                13,
                "I want to learn something new on a computer. I would:",
                listOf(
                    LearningStyleOption("Talk with people who know about the program", LearningPreference.AUDITORY),
                    LearningStyleOption("Start using it and learn by trial and error", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Read the written instructions that came with it", LearningPreference.READ_WRITE),
                    LearningStyleOption("Follow the diagrams in a book", LearningPreference.VISUAL)
                )
            ),
            LearningStyleQuestion(
                14,
                "When I am learning, I:",
                listOf(
                    LearningStyleOption("See patterns in things", LearningPreference.VISUAL),
                    LearningStyleOption("Read books, articles and handouts", LearningPreference.READ_WRITE),
                    LearningStyleOption("Use examples and applications", LearningPreference.KINESTHETIC),
                    LearningStyleOption("Like to talk things through", LearningPreference.AUDITORY)
                )
            ),
            LearningStyleQuestion(
                15,
                "I want to save money and decide between options. I would:",
                listOf(
                    LearningStyleOption("Read a brochure that describes the options in detail", LearningPreference.READ_WRITE),
                    LearningStyleOption("Talk with an expert about the options", LearningPreference.AUDITORY),
                    LearningStyleOption("Use graphs showing different options for time periods", LearningPreference.VISUAL),
                    LearningStyleOption("Consider examples using my financial information", LearningPreference.KINESTHETIC)
                )
            )
        ))
    }

    override fun getTestType(): TestType = TestType.PERSONALITY

    override fun calculateScore(): Int {
        // For learning style, return the dominant preference percentage
        val totalResponses = visualScore + auditoryScore + readWriteScore + kinestheticScore
        val maxScore = maxOf(visualScore, auditoryScore, readWriteScore, kinestheticScore)
        return if (totalResponses > 0) (maxScore * 100) / totalResponses else 0
    }

    override fun generateDetailedScores(): Map<String, Double> {
        val totalResponses = visualScore + auditoryScore + readWriteScore + kinestheticScore

        return if (totalResponses > 0) {
            mapOf(
                "Visual" to (visualScore.toDouble() / totalResponses * 100),
                "Auditory" to (auditoryScore.toDouble() / totalResponses * 100),
                "Read/Write" to (readWriteScore.toDouble() / totalResponses * 100),
                "Kinesthetic" to (kinestheticScore.toDouble() / totalResponses * 100)
            )
        } else {
            mapOf(
                "Visual" to 25.0,
                "Auditory" to 25.0,
                "Read/Write" to 25.0,
                "Kinesthetic" to 25.0
            )
        }
    }

    override fun generateInsights(): List<String> {
        val insights = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        val dominantStyle = detailedScores.maxByOrNull { it.value }

        dominantStyle?.let { (style, percentage) ->
            when (style) {
                "Visual" -> insights.add("You are primarily a Visual learner (${percentage.toInt()}%) - you learn best through seeing and visualizing information")
                "Auditory" -> insights.add("You are primarily an Auditory learner (${percentage.toInt()}%) - you learn best through listening and discussing")
                "Read/Write" -> insights.add("You are primarily a Read/Write learner (${percentage.toInt()}%) - you learn best through reading and writing")
                "Kinesthetic" -> insights.add("You are primarily a Kinesthetic learner (${percentage.toInt()}%) - you learn best through hands-on experience")
                else -> insights.add("You have a balanced learning style (${percentage.toInt()}%)")
            }
        }

        // Check for multimodal preferences
        val strongPreferences = detailedScores.filter { it.value >= 25 }
        if (strongPreferences.size > 1) {
            insights.add("You have a multimodal learning style, combining ${strongPreferences.keys.joinToString(" and ")} preferences")
        }

        return insights
    }

    override fun generateRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val detailedScores = generateDetailedScores()

        detailedScores.forEach { (style, percentage) ->
            if (percentage >= 25) {
                when (style) {
                    "Visual" -> {
                        recommendations.add("Use mind maps, diagrams, and charts when studying")
                        recommendations.add("Highlight important text with different colors")
                        recommendations.add("Watch educational videos and visual demonstrations")
                    }
                    "Auditory" -> {
                        recommendations.add("Participate in group discussions and study groups")
                        recommendations.add("Listen to podcasts and audio recordings")
                        recommendations.add("Read aloud or explain concepts to others")
                    }
                    "Read/Write" -> {
                        recommendations.add("Take detailed notes and rewrite them for review")
                        recommendations.add("Read extensively and create written summaries")
                        recommendations.add("Use lists, bullet points, and written instructions")
                    }
                    "Kinesthetic" -> {
                        recommendations.add("Use hands-on activities and practical exercises")
                        recommendations.add("Take breaks and move around while studying")
                        recommendations.add("Use real-world examples and case studies")
                    }
                }
            }
        }

        recommendations.add("Combine multiple learning styles for maximum effectiveness")
        recommendations.add("Adapt your study environment to match your learning preferences")

        return recommendations
    }
}
