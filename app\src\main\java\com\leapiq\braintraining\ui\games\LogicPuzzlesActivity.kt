package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityLogicPuzzlesBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import com.leapiq.braintraining.ui.games.adapter.LogicGridAdapter
import com.leapiq.braintraining.ui.games.adapter.CluesAdapter
import com.leapiq.braintraining.ui.games.model.LogicPuzzle
import com.leapiq.braintraining.ui.games.model.Category
import com.leapiq.braintraining.ui.games.model.Clue
import com.leapiq.braintraining.ui.games.model.ClueType
import com.leapiq.braintraining.ui.games.model.CellState
import java.util.Date

/**
 * Logic Puzzles Game
 * Grid-based constraint satisfaction puzzles
 * Use clues to determine which attributes belong to which entities
 */
class LogicPuzzlesActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLogicPuzzlesBinding
    private lateinit var progressManager: GameProgressManager
    private lateinit var gridAdapter: LogicGridAdapter
    private lateinit var cluesAdapter: CluesAdapter

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "logic_6"

    // Logic puzzle specific
    private lateinit var currentPuzzle: LogicPuzzle
    private var puzzleStartTime = 0L
    private var currentPuzzleIndex = 1
    private var puzzlesPerRound = 2
    private var gridState = mutableMapOf<Pair<Int, Int>, CellState>()
    private var hintsUsed = 0
    private var maxHints = 3

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLogicPuzzlesBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.logic_puzzles)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Use clues to fill the grid. Mark X for impossible, O for confirmed!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup hint button
            btnHint.setOnClickListener {
                showHint()
            }

            // Setup check solution button
            btnCheckSolution.setOnClickListener {
                checkSolution()
            }

            // Setup reset button
            btnReset.setOnClickListener {
                resetPuzzle()
            }

            // Setup RecyclerViews
            setupRecyclerViews()
        }
    }

    private fun setupRecyclerViews() {
        // Setup grid
        gridAdapter = LogicGridAdapter(currentPuzzle.categories) { row, col, state ->
            onCellClicked(row, col, state)
        }
        
        binding.gridRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@LogicPuzzlesActivity)
            adapter = gridAdapter
        }

        // Setup clues
        cluesAdapter = CluesAdapter(currentPuzzle.clues) { clueIndex ->
            onClueClicked(clueIndex)
        }
        
        binding.cluesRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@LogicPuzzlesActivity)
            adapter = cluesAdapter
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentPuzzleIndex = 1
        puzzlesPerRound = getPuzzlesPerRound(currentLevel)
        hintsUsed = 0
        maxHints = getMaxHints(currentLevel)
        
        binding.puzzleText.text = "Puzzle $currentPuzzleIndex/$puzzlesPerRound"
        binding.hintsText.text = "Hints: $hintsUsed/$maxHints"
        
        generatePuzzle()
    }

    private fun getPuzzlesPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 2       // 2 puzzles per round
            in 6..10 -> 3      // 3 puzzles per round
            in 11..15 -> 3     // 3 puzzles per round
            in 16..20 -> 4     // 4 puzzles per round
            else -> 4          // 4 puzzles per round
        }
    }

    private fun getMaxHints(level: Int): Int {
        return when (level) {
            in 1..5 -> 5       // 5 hints for beginners
            in 6..10 -> 4      // 4 hints for easy
            in 11..15 -> 3     // 3 hints for medium
            in 16..20 -> 2     // 2 hints for hard
            else -> 1          // 1 hint for expert
        }
    }

    private fun getGridSize(level: Int): Int {
        return when (level) {
            in 1..3 -> 3       // 3x3 grid for beginners
            in 4..6 -> 4       // 4x4 grid for easy
            in 7..15 -> 5      // 5x5 grid for medium/hard
            else -> 6          // 6x6 grid for expert
        }
    }

    private fun generatePuzzle() {
        puzzleStartTime = System.currentTimeMillis()
        gridState.clear()
        
        val gridSize = getGridSize(currentLevel)
        currentPuzzle = createPuzzleForLevel(currentLevel, gridSize)
        
        // Update UI
        setupRecyclerViews()
        updateUI()
        
        binding.instructionText.text = "Read the clues and use logic to fill the grid!"
    }

    private fun createPuzzleForLevel(level: Int, gridSize: Int): LogicPuzzle {
        // Create categories based on grid size and level
        val categories = when (gridSize) {
            3 -> createBeginnerCategories()
            4 -> createEasyCategories()
            5 -> createMediumCategories()
            6 -> createHardCategories()
            else -> createMediumCategories()
        }
        
        // Generate clues based on level difficulty
        val clues = generateCluesForLevel(level, categories)
        
        // Create solution (this would be more complex in a real implementation)
        val solution = generateSolution(categories)
        
        return LogicPuzzle(categories, clues, solution)
    }

    private fun createBeginnerCategories(): List<Category> {
        return listOf(
            Category("People", listOf("Alice", "Bob", "Carol")),
            Category("Pets", listOf("Cat", "Dog", "Bird")),
            Category("Colors", listOf("Red", "Blue", "Green"))
        )
    }

    private fun createEasyCategories(): List<Category> {
        return listOf(
            Category("People", listOf("Alice", "Bob", "Carol", "Dave")),
            Category("Pets", listOf("Cat", "Dog", "Bird", "Fish")),
            Category("Colors", listOf("Red", "Blue", "Green", "Yellow")),
            Category("Drinks", listOf("Tea", "Coffee", "Juice", "Water"))
        )
    }

    private fun createMediumCategories(): List<Category> {
        return listOf(
            Category("People", listOf("Alice", "Bob", "Carol", "Dave", "Eve")),
            Category("Houses", listOf("House 1", "House 2", "House 3", "House 4", "House 5")),
            Category("Pets", listOf("Cat", "Dog", "Bird", "Fish", "Rabbit")),
            Category("Colors", listOf("Red", "Blue", "Green", "Yellow", "White")),
            Category("Drinks", listOf("Tea", "Coffee", "Juice", "Water", "Soda"))
        )
    }

    private fun createHardCategories(): List<Category> {
        return listOf(
            Category("People", listOf("Alice", "Bob", "Carol", "Dave", "Eve", "Frank")),
            Category("Houses", listOf("House 1", "House 2", "House 3", "House 4", "House 5", "House 6")),
            Category("Pets", listOf("Cat", "Dog", "Bird", "Fish", "Rabbit", "Hamster")),
            Category("Colors", listOf("Red", "Blue", "Green", "Yellow", "White", "Black")),
            Category("Drinks", listOf("Tea", "Coffee", "Juice", "Water", "Soda", "Milk")),
            Category("Jobs", listOf("Doctor", "Teacher", "Engineer", "Artist", "Chef", "Lawyer"))
        )
    }

    private fun generateCluesForLevel(level: Int, categories: List<Category>): List<Clue> {
        // This is a simplified clue generation - in a real implementation,
        // you'd want to generate clues that lead to a unique solution
        val clues = mutableListOf<Clue>()
        
        when (level) {
            in 1..3 -> {
                // Beginner: Direct clues
                clues.add(Clue("Alice has a Cat", ClueType.DIRECT))
                clues.add(Clue("Bob likes Red", ClueType.DIRECT))
                clues.add(Clue("Carol doesn't have a Dog", ClueType.NOT_EQUAL))
                clues.add(Clue("The person with Blue doesn't have a Bird", ClueType.NOT_EQUAL))
                clues.add(Clue("Alice doesn't like Green", ClueType.NOT_EQUAL))
            }
            in 4..6 -> {
                // Easy: Mix of direct and negative clues
                clues.add(Clue("Alice has a Cat", ClueType.DIRECT))
                clues.add(Clue("Bob doesn't have a Fish", ClueType.NOT_EQUAL))
                clues.add(Clue("Carol likes Blue", ClueType.DIRECT))
                clues.add(Clue("Dave drinks Tea", ClueType.DIRECT))
                clues.add(Clue("The person with Red doesn't drink Coffee", ClueType.NOT_EQUAL))
                clues.add(Clue("Alice doesn't drink Water", ClueType.NOT_EQUAL))
                clues.add(Clue("The Dog owner likes Yellow", ClueType.DIRECT))
            }
            else -> {
                // Medium/Hard: Complex clues with relationships
                clues.add(Clue("Alice lives in House 1", ClueType.DIRECT))
                clues.add(Clue("The Cat owner lives next to the Dog owner", ClueType.ADJACENT))
                clues.add(Clue("Bob doesn't live in House 5", ClueType.NOT_EQUAL))
                clues.add(Clue("The person in the Red house drinks Coffee", ClueType.DIRECT))
                clues.add(Clue("Carol lives immediately left of Dave", ClueType.ADJACENT))
                clues.add(Clue("The Blue house is somewhere left of the Green house", ClueType.RELATIVE))
                clues.add(Clue("Eve has a Bird and drinks Tea", ClueType.DIRECT))
                clues.add(Clue("The Fish owner doesn't live in House 3", ClueType.NOT_EQUAL))
            }
        }
        
        return clues
    }

    private fun generateSolution(categories: List<Category>): Map<String, Map<String, String>> {
        // This is a placeholder - in a real implementation, you'd generate
        // a valid solution that satisfies all clues
        val solution = mutableMapOf<String, Map<String, String>>()
        
        // For now, return empty solution
        return solution
    }

    private fun onCellClicked(row: Int, col: Int, currentState: CellState) {
        val newState = when (currentState) {
            CellState.EMPTY -> CellState.MARKED_X
            CellState.MARKED_X -> CellState.MARKED_O
            CellState.MARKED_O -> CellState.EMPTY
        }
        
        gridState[Pair(row, col)] = newState
        gridAdapter.updateCellState(row, col, newState)
        
        // Check if puzzle is solved
        if (isPuzzleSolved()) {
            puzzleComplete()
        }
    }

    private fun onClueClicked(clueIndex: Int) {
        // Highlight the clue or show explanation
        cluesAdapter.highlightClue(clueIndex)
        
        // Show clue explanation
        val clue = currentPuzzle.clues[clueIndex]
        Toast.makeText(this, "Clue: ${clue.text}", Toast.LENGTH_SHORT).show()
    }

    private fun isPuzzleSolved(): Boolean {
        // Check if the current grid state matches the solution
        // This is a simplified check - in a real implementation,
        // you'd validate against the actual solution
        
        // For now, check if enough cells are filled
        val totalCells = currentPuzzle.categories.size * currentPuzzle.categories.size
        val filledCells = gridState.values.count { it == CellState.MARKED_O }
        
        return filledCells >= currentPuzzle.categories.size // At least one O per row/column
    }

    private fun puzzleComplete() {
        val puzzleTime = System.currentTimeMillis() - puzzleStartTime
        totalCorrect++
        totalAttempts++
        
        showMessage("Puzzle solved! Great logic!")
        
        Handler(Looper.getMainLooper()).postDelayed({
            if (currentPuzzleIndex < puzzlesPerRound) {
                nextPuzzle()
            } else {
                roundComplete()
            }
        }, 2000)
    }

    private fun nextPuzzle() {
        currentPuzzleIndex++
        binding.puzzleText.text = "Puzzle $currentPuzzleIndex/$puzzlesPerRound"
        generatePuzzle()
    }

    private fun roundComplete() {
        val roundTime = System.currentTimeMillis() - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts * 100) else 0.0
        
        val roundResult = RoundResult(
            roundNumber = currentRound,
            isCorrect = accuracy > 0.7,
            timeSpentMs = roundTime,
            attempts = puzzlesPerRound
        )
        
        if (currentRound < maxRounds) {
            currentRound++
            binding.roundText.text = "Round $currentRound/$maxRounds"
            setupGame()
        } else {
            gameComplete(listOf(roundResult))
        }
    }

    private fun gameComplete(roundResults: List<RoundResult>) {
        val totalTime = System.currentTimeMillis() - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )
        
        progressManager.saveLevelResult(levelResult)
        
        // Navigate to results
        val intent = android.content.Intent(this, GameResultActivity::class.java).apply {
            putExtra(GameResultActivity.EXTRA_GAME_ID, gameId)
            putExtra(GameResultActivity.EXTRA_LEVEL, currentLevel)
            putExtra(GameResultActivity.EXTRA_ACCURACY, (accuracy * 100).toInt())
            putExtra(GameResultActivity.EXTRA_TIME_MS, totalTime)
            putExtra(GameResultActivity.EXTRA_SCORE, (accuracy * 100).toInt())
            putExtra(GameResultActivity.EXTRA_CORRECT_ROUNDS, totalCorrect)
            putExtra(GameResultActivity.EXTRA_TOTAL_ROUNDS, totalAttempts)
        }
        startActivity(intent)
        finish()
    }

    private fun updateUI() {
        binding.apply {
            hintsText.text = "Hints: $hintsUsed/$maxHints"
            btnHint.isEnabled = hintsUsed < maxHints
        }
    }

    private fun showHint() {
        if (hintsUsed >= maxHints) {
            Toast.makeText(this, "No more hints available!", Toast.LENGTH_SHORT).show()
            return
        }
        
        hintsUsed++
        updateUI()
        
        // Show a helpful hint based on current puzzle state
        val hint = generateHint()
        
        AlertDialog.Builder(this)
            .setTitle("Hint")
            .setMessage(hint)
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun generateHint(): String {
        // Generate contextual hints based on current state
        val hints = listOf(
            "Look for direct clues first - they give you definite answers!",
            "If someone doesn't have something, mark it with X.",
            "When you confirm a match with O, eliminate other possibilities in that row/column.",
            "Use process of elimination - if 2 out of 3 options are X, the third must be O.",
            "Adjacent clues mean next to each other in the sequence.",
            "Read each clue carefully - some have multiple parts!"
        )
        
        return hints.random()
    }

    private fun checkSolution() {
        if (isPuzzleSolved()) {
            puzzleComplete()
        } else {
            // Show validation feedback
            val errors = validateCurrentState()
            if (errors.isNotEmpty()) {
                AlertDialog.Builder(this)
                    .setTitle("Solution Check")
                    .setMessage("Found ${errors.size} error(s). Keep working on it!")
                    .setPositiveButton("Continue") { dialog, _ ->
                        dialog.dismiss()
                    }
                    .show()
            } else {
                Toast.makeText(this, "Looking good so far! Keep going.", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun validateCurrentState(): List<String> {
        // Validate current grid state against clues
        val errors = mutableListOf<String>()
        
        // This would contain actual validation logic
        // For now, return empty list
        
        return errors
    }

    private fun resetPuzzle() {
        AlertDialog.Builder(this)
            .setTitle("Reset Puzzle")
            .setMessage("Are you sure you want to reset the current puzzle?")
            .setPositiveButton("Reset") { _, _ ->
                gridState.clear()
                gridAdapter.resetGrid()
                puzzleStartTime = System.currentTimeMillis()
                binding.instructionText.text = "Puzzle reset! Start fresh with the clues."
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun showMessage(message: String) {
        binding.instructionText.text = message
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Use clues to fill the grid. Mark X for impossible, O for confirmed!"
        }, 2000)
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        AlertDialog.Builder(this)
            .setView(dialogView)
            .show()
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}
