<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_white"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_close"
        app:title="Test"
        app:titleTextColor="@color/text_primary" />

    <!-- Progress Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/surface_white"
        android:padding="16dp"
        android:elevation="2dp">

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginBottom="8dp"
            android:progressTint="@color/primary_light_blue"
            android:progressBackgroundTint="@color/background_light_gray"
            android:progress="0"
            android:max="100" />

        <!-- Progress Text -->
        <TextView
            android:id="@+id/progress_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Question 1 of 15"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:layout_gravity="center" />

    </LinearLayout>

    <!-- Question Content Area -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Question Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/surface_white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Question Text -->
                    <TextView
                        android:id="@+id/question_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Question text will appear here..."
                        android:textSize="18sp"
                        android:textColor="@color/text_primary"
                        android:lineSpacing="1.3"
                        android:layout_marginBottom="20dp" />

                    <!-- Dynamic Content Container -->
                    <LinearLayout
                        android:id="@+id/question_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- Navigation Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/surface_white"
        android:padding="16dp"
        android:elevation="8dp">

        <!-- Previous Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_previous"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Previous"
            android:textColor="@color/primary_light_blue"
            app:strokeColor="@color/primary_light_blue"
            app:cornerRadius="24dp"
            android:visibility="gone" />

        <!-- Next/Finish Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_next"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Next"
            android:textColor="@color/surface_white"
            app:backgroundTint="@color/primary_light_blue"
            app:cornerRadius="24dp" />

    </LinearLayout>

</LinearLayout>
