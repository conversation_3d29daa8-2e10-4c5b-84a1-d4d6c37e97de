// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMemoryAssessmentBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btn0;

  @NonNull
  public final Button btn1;

  @NonNull
  public final Button btn2;

  @NonNull
  public final Button btn3;

  @NonNull
  public final Button btn4;

  @NonNull
  public final Button btn5;

  @NonNull
  public final Button btn6;

  @NonNull
  public final Button btn7;

  @NonNull
  public final Button btn8;

  @NonNull
  public final Button btn9;

  @NonNull
  public final Button btnClearResponse;

  @NonNull
  public final Button btnSubmitResponse;

  @NonNull
  public final LinearLayout delayedRecallContainer;

  @NonNull
  public final TextView delayedRecallText;

  @NonNull
  public final TextView digitDisplay;

  @NonNull
  public final LinearLayout digitSpanContainer;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final RecyclerView memoryGrid;

  @NonNull
  public final LinearLayout numberPadContainer;

  @NonNull
  public final LinearLayout patternRecallContainer;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final TextView responseDisplay;

  @NonNull
  public final LinearLayout spatialSpanContainer;

  @NonNull
  public final TextView taskTypeText;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final EditText wordInput;

  @NonNull
  public final LinearLayout wordInputContainer;

  @NonNull
  public final LinearLayout wordListContainer;

  @NonNull
  public final TextView wordListDisplay;

  private ActivityMemoryAssessmentBinding(@NonNull LinearLayout rootView, @NonNull Button btn0,
      @NonNull Button btn1, @NonNull Button btn2, @NonNull Button btn3, @NonNull Button btn4,
      @NonNull Button btn5, @NonNull Button btn6, @NonNull Button btn7, @NonNull Button btn8,
      @NonNull Button btn9, @NonNull Button btnClearResponse, @NonNull Button btnSubmitResponse,
      @NonNull LinearLayout delayedRecallContainer, @NonNull TextView delayedRecallText,
      @NonNull TextView digitDisplay, @NonNull LinearLayout digitSpanContainer,
      @NonNull TextView instructionText, @NonNull RecyclerView memoryGrid,
      @NonNull LinearLayout numberPadContainer, @NonNull LinearLayout patternRecallContainer,
      @NonNull ProgressBar progressBar, @NonNull TextView progressText,
      @NonNull TextView responseDisplay, @NonNull LinearLayout spatialSpanContainer,
      @NonNull TextView taskTypeText, @NonNull Toolbar toolbar, @NonNull EditText wordInput,
      @NonNull LinearLayout wordInputContainer, @NonNull LinearLayout wordListContainer,
      @NonNull TextView wordListDisplay) {
    this.rootView = rootView;
    this.btn0 = btn0;
    this.btn1 = btn1;
    this.btn2 = btn2;
    this.btn3 = btn3;
    this.btn4 = btn4;
    this.btn5 = btn5;
    this.btn6 = btn6;
    this.btn7 = btn7;
    this.btn8 = btn8;
    this.btn9 = btn9;
    this.btnClearResponse = btnClearResponse;
    this.btnSubmitResponse = btnSubmitResponse;
    this.delayedRecallContainer = delayedRecallContainer;
    this.delayedRecallText = delayedRecallText;
    this.digitDisplay = digitDisplay;
    this.digitSpanContainer = digitSpanContainer;
    this.instructionText = instructionText;
    this.memoryGrid = memoryGrid;
    this.numberPadContainer = numberPadContainer;
    this.patternRecallContainer = patternRecallContainer;
    this.progressBar = progressBar;
    this.progressText = progressText;
    this.responseDisplay = responseDisplay;
    this.spatialSpanContainer = spatialSpanContainer;
    this.taskTypeText = taskTypeText;
    this.toolbar = toolbar;
    this.wordInput = wordInput;
    this.wordInputContainer = wordInputContainer;
    this.wordListContainer = wordListContainer;
    this.wordListDisplay = wordListDisplay;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMemoryAssessmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMemoryAssessmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_memory_assessment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMemoryAssessmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_0;
      Button btn0 = ViewBindings.findChildViewById(rootView, id);
      if (btn0 == null) {
        break missingId;
      }

      id = R.id.btn_1;
      Button btn1 = ViewBindings.findChildViewById(rootView, id);
      if (btn1 == null) {
        break missingId;
      }

      id = R.id.btn_2;
      Button btn2 = ViewBindings.findChildViewById(rootView, id);
      if (btn2 == null) {
        break missingId;
      }

      id = R.id.btn_3;
      Button btn3 = ViewBindings.findChildViewById(rootView, id);
      if (btn3 == null) {
        break missingId;
      }

      id = R.id.btn_4;
      Button btn4 = ViewBindings.findChildViewById(rootView, id);
      if (btn4 == null) {
        break missingId;
      }

      id = R.id.btn_5;
      Button btn5 = ViewBindings.findChildViewById(rootView, id);
      if (btn5 == null) {
        break missingId;
      }

      id = R.id.btn_6;
      Button btn6 = ViewBindings.findChildViewById(rootView, id);
      if (btn6 == null) {
        break missingId;
      }

      id = R.id.btn_7;
      Button btn7 = ViewBindings.findChildViewById(rootView, id);
      if (btn7 == null) {
        break missingId;
      }

      id = R.id.btn_8;
      Button btn8 = ViewBindings.findChildViewById(rootView, id);
      if (btn8 == null) {
        break missingId;
      }

      id = R.id.btn_9;
      Button btn9 = ViewBindings.findChildViewById(rootView, id);
      if (btn9 == null) {
        break missingId;
      }

      id = R.id.btn_clear_response;
      Button btnClearResponse = ViewBindings.findChildViewById(rootView, id);
      if (btnClearResponse == null) {
        break missingId;
      }

      id = R.id.btn_submit_response;
      Button btnSubmitResponse = ViewBindings.findChildViewById(rootView, id);
      if (btnSubmitResponse == null) {
        break missingId;
      }

      id = R.id.delayed_recall_container;
      LinearLayout delayedRecallContainer = ViewBindings.findChildViewById(rootView, id);
      if (delayedRecallContainer == null) {
        break missingId;
      }

      id = R.id.delayed_recall_text;
      TextView delayedRecallText = ViewBindings.findChildViewById(rootView, id);
      if (delayedRecallText == null) {
        break missingId;
      }

      id = R.id.digit_display;
      TextView digitDisplay = ViewBindings.findChildViewById(rootView, id);
      if (digitDisplay == null) {
        break missingId;
      }

      id = R.id.digit_span_container;
      LinearLayout digitSpanContainer = ViewBindings.findChildViewById(rootView, id);
      if (digitSpanContainer == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.memory_grid;
      RecyclerView memoryGrid = ViewBindings.findChildViewById(rootView, id);
      if (memoryGrid == null) {
        break missingId;
      }

      id = R.id.number_pad_container;
      LinearLayout numberPadContainer = ViewBindings.findChildViewById(rootView, id);
      if (numberPadContainer == null) {
        break missingId;
      }

      id = R.id.pattern_recall_container;
      LinearLayout patternRecallContainer = ViewBindings.findChildViewById(rootView, id);
      if (patternRecallContainer == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progress_text;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.response_display;
      TextView responseDisplay = ViewBindings.findChildViewById(rootView, id);
      if (responseDisplay == null) {
        break missingId;
      }

      id = R.id.spatial_span_container;
      LinearLayout spatialSpanContainer = ViewBindings.findChildViewById(rootView, id);
      if (spatialSpanContainer == null) {
        break missingId;
      }

      id = R.id.task_type_text;
      TextView taskTypeText = ViewBindings.findChildViewById(rootView, id);
      if (taskTypeText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.word_input;
      EditText wordInput = ViewBindings.findChildViewById(rootView, id);
      if (wordInput == null) {
        break missingId;
      }

      id = R.id.word_input_container;
      LinearLayout wordInputContainer = ViewBindings.findChildViewById(rootView, id);
      if (wordInputContainer == null) {
        break missingId;
      }

      id = R.id.word_list_container;
      LinearLayout wordListContainer = ViewBindings.findChildViewById(rootView, id);
      if (wordListContainer == null) {
        break missingId;
      }

      id = R.id.word_list_display;
      TextView wordListDisplay = ViewBindings.findChildViewById(rootView, id);
      if (wordListDisplay == null) {
        break missingId;
      }

      return new ActivityMemoryAssessmentBinding((LinearLayout) rootView, btn0, btn1, btn2, btn3,
          btn4, btn5, btn6, btn7, btn8, btn9, btnClearResponse, btnSubmitResponse,
          delayedRecallContainer, delayedRecallText, digitDisplay, digitSpanContainer,
          instructionText, memoryGrid, numberPadContainer, patternRecallContainer, progressBar,
          progressText, responseDisplay, spatialSpanContainer, taskTypeText, toolbar, wordInput,
          wordInputContainer, wordListContainer, wordListDisplay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
