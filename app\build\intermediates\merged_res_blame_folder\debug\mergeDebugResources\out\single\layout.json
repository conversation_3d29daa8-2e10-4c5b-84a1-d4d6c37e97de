[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_search_shape.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_search_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_attention_test.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_attention_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_pattern_completion.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_pattern_completion.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_logical_reasoning.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_logical_reasoning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_word_search.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_word_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_memory_card.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_memory_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_focus_challenge.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_focus_challenge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_sequence_recall.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_sequence_recall.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_number_sequences.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_number_sequences.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_achievement.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_achievement.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_pattern_cell.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_pattern_cell.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_logic_puzzles.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_logic_puzzles.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_reaction_time.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_reaction_time.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_game_card.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_game_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\fragment_today.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_today.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_all_results.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_all_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_pattern_memory.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_pattern_memory.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_test_instruction.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_test_instruction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_recent_activity.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_recent_activity.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_stress_response.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_stress_response.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\fragment_games.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_games.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_test_result.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_test_result.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\fragment_profile.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_word_association.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_word_association.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_anagrams.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_anagrams.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_tower_of_hanoi.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_tower_of_hanoi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_memory_grid.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_memory_grid.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_tube_sort.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_tube_sort.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_daily_challenge.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_daily_challenge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_category_accuracy.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_category_accuracy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_visual_search.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_visual_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_memory_assessment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_memory_assessment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_logic_grid_row.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_logic_grid_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_mental_arithmetic.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_mental_arithmetic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_detailed_score.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_detailed_score.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_test_results.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_test_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_word_search_word.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_word_search_word.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_test_question_base.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_test_question_base.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_test_info.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_test_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_learning_style_option.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_learning_style_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_speed_math.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_speed_math.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_test_card.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_test_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_learning_style.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_learning_style.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\fragment_tests.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_tests.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_analytics.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_analytics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_my_results.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_my_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_tube.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_tube.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_spatial_rotation.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_spatial_rotation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_problem_solving_style.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_problem_solving_style.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_processing_speed.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_processing_speed.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_all_results.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_all_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\layout_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\layout_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_problem_solving_option.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_problem_solving_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_logic_grid_cell.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_logic_grid_cell.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_vocabulary.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_vocabulary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\dialog_game_menu.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\dialog_game_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_test_progress.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_test_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_hanoi_tower.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_hanoi_tower.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_stroop_test.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_stroop_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_game_result.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_game_result.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_logic_clue.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_logic_clue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_card_matching.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_card_matching.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_estimation.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_estimation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_test_progress.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_test_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_stress_response_option.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_stress_response_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\activity_number_memory.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_number_memory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\fragment_progress.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_word_search_cell.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_word_search_cell.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-mergeDebugResources-3:\\layout\\item_logic_grid_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_logic_grid_header.xml"}]