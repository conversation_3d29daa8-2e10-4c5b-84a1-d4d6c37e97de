<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_processing_speed" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_processing_speed.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_processing_speed_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="357" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/progress_text" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="42"/></Target><Target id="@+id/task_type_text" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="50" endOffset="41"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="54" startOffset="8" endLine="60" endOffset="62"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="65" startOffset="4" endLine="75" endOffset="32"/></Target><Target id="@+id/feedback_text" view="TextView"><Expressions/><location startLine="90" startOffset="12" endLine="100" endOffset="43"/></Target><Target id="@+id/simple_reaction_container" view="LinearLayout"><Expressions/><location startLine="103" startOffset="12" endLine="133" endOffset="26"/></Target><Target id="@+id/stimulus_display" view="TextView"><Expressions/><location startLine="110" startOffset="16" endLine="121" endOffset="71"/></Target><Target id="@+id/simple_response_button" view="Button"><Expressions/><location startLine="123" startOffset="16" endLine="131" endOffset="62"/></Target><Target id="@+id/choice_reaction_container" view="LinearLayout"><Expressions/><location startLine="136" startOffset="12" endLine="208" endOffset="26"/></Target><Target id="@+id/choice_stimulus" view="TextView"><Expressions/><location startLine="143" startOffset="16" endLine="154" endOffset="71"/></Target><Target id="@+id/red_button" view="Button"><Expressions/><location startLine="162" startOffset="20" endLine="171" endOffset="66"/></Target><Target id="@+id/blue_button" view="Button"><Expressions/><location startLine="173" startOffset="20" endLine="182" endOffset="66"/></Target><Target id="@+id/green_button" view="Button"><Expressions/><location startLine="184" startOffset="20" endLine="193" endOffset="66"/></Target><Target id="@+id/yellow_button" view="Button"><Expressions/><location startLine="195" startOffset="20" endLine="204" endOffset="65"/></Target><Target id="@+id/symbol_coding_container" view="LinearLayout"><Expressions/><location startLine="211" startOffset="12" endLine="296" endOffset="26"/></Target><Target id="@+id/symbol_mapping_text" view="TextView"><Expressions/><location startLine="219" startOffset="16" endLine="229" endOffset="44"/></Target><Target id="@+id/symbol_display" view="TextView"><Expressions/><location startLine="231" startOffset="16" endLine="242" endOffset="71"/></Target><Target id="@+id/symbol_1_button" view="Button"><Expressions/><location startLine="250" startOffset="20" endLine="259" endOffset="65"/></Target><Target id="@+id/symbol_2_button" view="Button"><Expressions/><location startLine="261" startOffset="20" endLine="270" endOffset="65"/></Target><Target id="@+id/symbol_3_button" view="Button"><Expressions/><location startLine="272" startOffset="20" endLine="281" endOffset="65"/></Target><Target id="@+id/symbol_4_button" view="Button"><Expressions/><location startLine="283" startOffset="20" endLine="292" endOffset="65"/></Target><Target id="@+id/decision_speed_container" view="LinearLayout"><Expressions/><location startLine="299" startOffset="12" endLine="351" endOffset="26"/></Target><Target id="@+id/decision_text" view="TextView"><Expressions/><location startLine="306" startOffset="16" endLine="317" endOffset="44"/></Target><Target id="@+id/yes_button" view="Button"><Expressions/><location startLine="325" startOffset="20" endLine="335" endOffset="66"/></Target><Target id="@+id/no_button" view="Button"><Expressions/><location startLine="337" startOffset="20" endLine="347" endOffset="66"/></Target></Targets></Layout>