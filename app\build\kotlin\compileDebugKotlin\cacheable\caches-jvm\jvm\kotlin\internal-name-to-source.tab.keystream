%com/leapiq/braintraining/MainActivity4com/leapiq/braintraining/analysis/TestAnalysisEngineucom/leapiq/braintraining/analysis/TestAnalysisEngine$generateImprovementRecommendations$$inlined$sortedByDescending$1mcom/leapiq/braintraining/analysis/TestAnalysisEngine$generatePersonalComparison$$inlined$sortedByDescending$1bcom/leapiq/braintraining/analysis/TestAnalysisEngine$calculateImprovementTrend$$inlined$sortedBy$1Acom/leapiq/braintraining/analysis/TestAnalysisEngine$WhenMappings2com/leapiq/braintraining/analysis/DetailedAnalysis4com/leapiq/braintraining/analysis/OverallPerformance5com/leapiq/braintraining/analysis/PerformanceCategory0com/leapiq/braintraining/analysis/TimeEfficiency5com/leapiq/braintraining/analysis/TimeEfficiencyLevel2com/leapiq/braintraining/analysis/ConsistencyScore2com/leapiq/braintraining/analysis/ConsistencyLevel2com/leapiq/braintraining/analysis/CognitiveProfile4com/leapiq/braintraining/analysis/PersonalityProfile=com/leapiq/braintraining/analysis/StrengthsWeaknessesAnalysis*com/leapiq/braintraining/analysis/Strength0com/leapiq/braintraining/analysis/StrengthImpact*com/leapiq/braintraining/analysis/Weakness2com/leapiq/braintraining/analysis/WeaknessPriority6com/leapiq/braintraining/analysis/ImprovementPotential1com/leapiq/braintraining/analysis/ResponsePattern5com/leapiq/braintraining/analysis/ResponsePatternType=com/leapiq/braintraining/analysis/ResponsePatternSignificance0com/leapiq/braintraining/analysis/OverallBalance;com/leapiq/braintraining/analysis/ImprovementRecommendation8com/leapiq/braintraining/analysis/RecommendationCategory8com/leapiq/braintraining/analysis/RecommendationPriority3com/leapiq/braintraining/analysis/ImprovementImpact;com/leapiq/braintraining/analysis/ComparativeAnalysisResult4com/leapiq/braintraining/analysis/PersonalComparison6com/leapiq/braintraining/analysis/PopulationComparison2com/leapiq/braintraining/analysis/ImprovementTrend0com/leapiq/braintraining/analysis/TrendDirection*com/leapiq/braintraining/analysis/NextStep2com/leapiq/braintraining/analysis/NextStepCategory2com/leapiq/braintraining/analysis/NextStepPriority1com/leapiq/braintraining/data/GameProgressManagerHcom/leapiq/braintraining/data/GameProgressManager$getLevelResults$type$1;com/leapiq/braintraining/data/GameProgressManager$Companion3com/leapiq/braintraining/data/TestDataBackupManagerecom/leapiq/braintraining/data/TestDataBackupManager$getAvailableBackups$$inlined$sortedByDescending$1=com/leapiq/braintraining/data/TestDataBackupManager$Companion(com/leapiq/braintraining/data/BackupData,com/leapiq/braintraining/data/BackupMetadata(com/leapiq/braintraining/data/BackupInfo*com/leapiq/braintraining/data/BackupResult2com/leapiq/braintraining/data/BackupResult$Success0com/leapiq/braintraining/data/BackupResult$Error+com/leapiq/braintraining/data/RestoreResult3com/leapiq/braintraining/data/RestoreResult$Success1com/leapiq/braintraining/data/RestoreResult$Error*com/leapiq/braintraining/data/ExportResult2com/leapiq/braintraining/data/ExportResult$Success0com/leapiq/braintraining/data/ExportResult$Error1com/leapiq/braintraining/data/TestProgressManagerGcom/leapiq/braintraining/data/TestProgressManager$getTestResults$type$1_com/leapiq/braintraining/data/TestProgressManager$calculateImprovementTrend$$inlined$sortedBy$1dcom/leapiq/braintraining/data/TestProgressManager$getRecentTestResults$$inlined$sortedByDescending$1Zcom/leapiq/braintraining/data/TestProgressManager$getPerformanceTrends$$inlined$sortedBy$1Gcom/leapiq/braintraining/data/TestProgressManager$importTestData$type$1Pcom/leapiq/braintraining/data/TestProgressManager$importTestData$1$resultsType$1\com/leapiq/braintraining/data/TestProgressManager$calculateCurrentStreak$$inlined$sortedBy$1^com/leapiq/braintraining/data/TestProgressManager$calculateImprovementRate$$inlined$sortedBy$1;com/leapiq/braintraining/data/TestProgressManager$Companion.com/leapiq/braintraining/data/PerformanceTrend,com/leapiq/braintraining/data/TrendDataPoint,com/leapiq/braintraining/data/TrendDirection,com/leapiq/braintraining/data/TestStatistics3com/leapiq/braintraining/data/TestResultsRepositoryccom/leapiq/braintraining/data/TestResultsRepository$getRecentActivity$$inlined$sortedByDescending$1ccom/leapiq/braintraining/data/TestResultsRepository$getStrongestAreas$$inlined$sortedByDescending$1[com/leapiq/braintraining/data/TestResultsRepository$getImprovementAreas$$inlined$sortedBy$1icom/leapiq/braintraining/data/TestResultsRepository$getPersonalizedInsights$$inlined$sortedByDescending$1bcom/leapiq/braintraining/data/TestResultsRepository$getPreviousScore$$inlined$sortedByDescending$1acom/leapiq/braintraining/data/TestResultsRepository$getTestRankings$$inlined$sortedByDescending$1=com/leapiq/braintraining/data/TestResultsRepository$Companion@com/leapiq/braintraining/data/TestResultsRepository$WhenMappings/com/leapiq/braintraining/data/TestDashboardData*com/leapiq/braintraining/data/ActivityItem*com/leapiq/braintraining/data/ActivityType*com/leapiq/braintraining/data/StrengthArea-com/leapiq/braintraining/data/ImprovementArea-com/leapiq/braintraining/data/OverallProgress1com/leapiq/braintraining/data/ComparativeAnalysis<com/leapiq/braintraining/data/CognitivePersonalityComparison)com/leapiq/braintraining/data/TestRanking-com/leapiq/braintraining/data/TestCorrelation5com/leapiq/braintraining/data/CorrelationSignificance1com/leapiq/braintraining/data/PersonalizedInsight)com/leapiq/braintraining/data/InsightType-com/leapiq/braintraining/data/InsightPriority/com/leapiq/braintraining/data/model/Achievement3com/leapiq/braintraining/data/model/AchievementType4com/leapiq/braintraining/data/model/CategoryAccuracy2com/leapiq/braintraining/data/model/DailyChallenge(com/leapiq/braintraining/data/model/Game0com/leapiq/braintraining/data/model/GameCategory,com/leapiq/braintraining/data/model/GameType/com/leapiq/braintraining/data/model/RoundResult/com/leapiq/braintraining/data/model/LevelResult0com/leapiq/braintraining/data/model/GameProgress0com/leapiq/braintraining/data/model/UserProgress(com/leapiq/braintraining/data/model/Test0com/leapiq/braintraining/data/model/TestCategory6com/leapiq/braintraining/data/model/TestQuestionResult.com/leapiq/braintraining/data/model/TestResult0com/leapiq/braintraining/data/model/TestProgress,com/leapiq/braintraining/data/model/TestType2com/leapiq/braintraining/ui/games/AnagramsActivity2com/leapiq/braintraining/ui/games/BaseGameActivity<com/leapiq/braintraining/ui/games/BaseGameActivity$Companion0com/leapiq/braintraining/ui/games/GameDifficulty6com/leapiq/braintraining/ui/games/CardMatchingActivityJcom/leapiq/braintraining/ui/games/CardMatchingActivity$setupRecyclerView$1Bcom/leapiq/braintraining/ui/games/CardMatchingActivity$LevelConfig4com/leapiq/braintraining/ui/games/EstimationActivityCcom/leapiq/braintraining/ui/games/EstimationActivity$EstimationTypeAcom/leapiq/braintraining/ui/games/EstimationActivity$WhenMappings8com/leapiq/braintraining/ui/games/FocusChallengeActivity.com/leapiq/braintraining/ui/games/FocusMetrics4com/leapiq/braintraining/ui/games/GameResultActivity>com/leapiq/braintraining/ui/games/GameResultActivity$Companion/com/leapiq/braintraining/ui/games/GamesFragment@com/leapiq/braintraining/ui/games/GamesFragment$setupGamesGrid$16com/leapiq/braintraining/ui/games/LogicPuzzlesActivityKcom/leapiq/braintraining/ui/games/LogicPuzzlesActivity$setupRecyclerViews$1Kcom/leapiq/braintraining/ui/games/LogicPuzzlesActivity$setupRecyclerViews$3Ccom/leapiq/braintraining/ui/games/LogicPuzzlesActivity$WhenMappings:com/leapiq/braintraining/ui/games/LogicalReasoningActivityEcom/leapiq/braintraining/ui/games/LogicalReasoningActivity$PuzzleTypeGcom/leapiq/braintraining/ui/games/LogicalReasoningActivity$WhenMappings:com/leapiq/braintraining/ui/games/MentalArithmeticActivityDcom/leapiq/braintraining/ui/games/MentalArithmeticActivity$OperationGcom/leapiq/braintraining/ui/games/MentalArithmeticActivity$WhenMappings6com/leapiq/braintraining/ui/games/NumberMemoryActivity9com/leapiq/braintraining/ui/games/NumberSequencesActivityEcom/leapiq/braintraining/ui/games/NumberSequencesActivity$PatternTypeFcom/leapiq/braintraining/ui/games/NumberSequencesActivity$WhenMappings;com/leapiq/braintraining/ui/games/PatternCompletionActivitybcom/leapiq/braintraining/ui/games/PatternCompletionActivity$generateMatrixPattern$displayPattern$1Gcom/leapiq/braintraining/ui/games/PatternCompletionActivity$PatternTypeHcom/leapiq/braintraining/ui/games/PatternCompletionActivity$WhenMappings7com/leapiq/braintraining/ui/games/PatternMemoryActivityCcom/leapiq/braintraining/ui/games/PatternMemoryActivity$setupGrid$16com/leapiq/braintraining/ui/games/ReactionTimeActivity8com/leapiq/braintraining/ui/games/SequenceRecallActivity9com/leapiq/braintraining/ui/games/SpatialRotationActivity?com/leapiq/braintraining/ui/games/SpatialRotationActivity$ShapeBcom/leapiq/braintraining/ui/games/SpatialRotationActivity$RotationFcom/leapiq/braintraining/ui/games/SpatialRotationActivity$WhenMappings3com/leapiq/braintraining/ui/games/SpeedMathActivityEcom/leapiq/braintraining/ui/games/SpeedMathActivity$startRoundTimer$1=com/leapiq/braintraining/ui/games/SpeedMathActivity$Operation@com/leapiq/braintraining/ui/games/SpeedMathActivity$WhenMappings4com/leapiq/braintraining/ui/games/StroopTestActivity6com/leapiq/braintraining/ui/games/TowerOfHanoiActivityDcom/leapiq/braintraining/ui/games/TowerOfHanoiActivity$setupTowers$12com/leapiq/braintraining/ui/games/TubeSortActivity>com/leapiq/braintraining/ui/games/TubeSortActivity$setupGrid$16com/leapiq/braintraining/ui/games/VisualSearchActivityBcom/leapiq/braintraining/ui/games/VisualSearchActivity$setupGrid$1Gcom/leapiq/braintraining/ui/games/VisualSearchActivity$SearchDifficultyCcom/leapiq/braintraining/ui/games/VisualSearchActivity$WhenMappings4com/leapiq/braintraining/ui/games/VocabularyActivityCcom/leapiq/braintraining/ui/games/VocabularyActivity$VocabularyModeAcom/leapiq/braintraining/ui/games/VocabularyActivity$WhenMappings9com/leapiq/braintraining/ui/games/WordAssociationActivity4com/leapiq/braintraining/ui/games/WordSearchActivityDcom/leapiq/braintraining/ui/games/WordSearchActivity$setupAdapters$16com/leapiq/braintraining/ui/games/adapter/CluesAdapterEcom/leapiq/braintraining/ui/games/adapter/CluesAdapter$ClueViewHolderCcom/leapiq/braintraining/ui/games/adapter/CluesAdapter$WhenMappings5com/leapiq/braintraining/ui/games/adapter/GameAdapterNcom/leapiq/braintraining/ui/games/adapter/GameAdapter$Companion$DiffCallback$1Dcom/leapiq/braintraining/ui/games/adapter/GameAdapter$GameViewHolderQcom/leapiq/braintraining/ui/games/adapter/GameAdapter$GameViewHolder$WhenMappings?com/leapiq/braintraining/ui/games/adapter/GameAdapter$Companion;com/leapiq/braintraining/ui/games/adapter/HanoiTowerAdapterKcom/leapiq/braintraining/ui/games/adapter/HanoiTowerAdapter$TowerViewHolderHcom/leapiq/braintraining/ui/games/adapter/HanoiTowerAdapter$WhenMappings:com/leapiq/braintraining/ui/games/adapter/LogicGridAdapterLcom/leapiq/braintraining/ui/games/adapter/LogicGridAdapter$GridRowViewHolderGcom/leapiq/braintraining/ui/games/adapter/LogicGridAdapter$WhenMappings<com/leapiq/braintraining/ui/games/adapter/PatternGridAdapterRcom/leapiq/braintraining/ui/games/adapter/PatternGridAdapter$PatternCellViewHolder5com/leapiq/braintraining/ui/games/adapter/TubeAdapterDcom/leapiq/braintraining/ui/games/adapter/TubeAdapter$TubeViewHolderBcom/leapiq/braintraining/ui/games/adapter/TubeAdapter$WhenMappings=com/leapiq/braintraining/ui/games/adapter/VisualSearchAdapterRcom/leapiq/braintraining/ui/games/adapter/VisualSearchAdapter$SearchItemViewHolder9com/leapiq/braintraining/ui/games/adapter/WordListAdapterHcom/leapiq/braintraining/ui/games/adapter/WordListAdapter$WordViewHolder?com/leapiq/braintraining/ui/games/adapter/WordSearchGridAdapterRcom/leapiq/braintraining/ui/games/adapter/WordSearchGridAdapter$GridCellViewHolder3com/leapiq/braintraining/ui/games/memory/MemoryCard:com/leapiq/braintraining/ui/games/memory/MemoryCardAdapterTcom/leapiq/braintraining/ui/games/memory/MemoryCardAdapter$flipCardWithAnimation$1$1ecom/leapiq/braintraining/ui/games/memory/MemoryCardAdapter$flipCardWithAnimation$1$1$onAnimationEnd$1Icom/leapiq/braintraining/ui/games/memory/MemoryCardAdapter$CardViewHolder,com/leapiq/braintraining/ui/games/model/Ball2com/leapiq/braintraining/ui/games/model/Ball$Color1com/leapiq/braintraining/ui/games/model/HanoiDisk;com/leapiq/braintraining/ui/games/model/HanoiDisk$DiskColor2com/leapiq/braintraining/ui/games/model/HanoiTower3com/leapiq/braintraining/ui/games/model/LogicPuzzle0com/leapiq/braintraining/ui/games/model/Category,com/leapiq/braintraining/ui/games/model/Clue0com/leapiq/braintraining/ui/games/model/ClueType1com/leapiq/braintraining/ui/games/model/CellState4com/leapiq/braintraining/ui/games/model/GridPosition1com/leapiq/braintraining/ui/games/model/GridState2com/leapiq/braintraining/ui/games/model/Constraint6com/leapiq/braintraining/ui/games/model/ConstraintType,com/leapiq/braintraining/ui/games/model/Hint0com/leapiq/braintraining/ui/games/model/HintType2com/leapiq/braintraining/ui/games/model/SearchItem,com/leapiq/braintraining/ui/games/model/Tube6com/leapiq/braintraining/ui/games/model/WordSearchGrid6com/leapiq/braintraining/ui/games/model/WordSearchWord3com/leapiq/braintraining/ui/profile/ProfileFragment5com/leapiq/braintraining/ui/progress/ProgressFragment?com/leapiq/braintraining/ui/progress/adapter/AchievementAdapterXcom/leapiq/braintraining/ui/progress/adapter/AchievementAdapter$Companion$DiffCallback$1Ucom/leapiq/braintraining/ui/progress/adapter/AchievementAdapter$AchievementViewHolderIcom/leapiq/braintraining/ui/progress/adapter/AchievementAdapter$CompanionDcom/leapiq/braintraining/ui/progress/adapter/CategoryAccuracyAdapter]com/leapiq/braintraining/ui/progress/adapter/CategoryAccuracyAdapter$Companion$DiffCallback$1Wcom/leapiq/braintraining/ui/progress/adapter/CategoryAccuracyAdapter$AccuracyViewHolderNcom/leapiq/braintraining/ui/progress/adapter/CategoryAccuracyAdapter$Companion2com/leapiq/braintraining/ui/tests/BaseTestActivity<com/leapiq/braintraining/ui/tests/BaseTestActivity$Companion6com/leapiq/braintraining/ui/tests/BaseTestInfoActivity@com/leapiq/braintraining/ui/tests/BaseTestInfoActivity$Companion4com/leapiq/braintraining/ui/tests/LearningPreference7com/leapiq/braintraining/ui/tests/LearningStyleQuestion5com/leapiq/braintraining/ui/tests/LearningStyleOption7com/leapiq/braintraining/ui/tests/LearningStyleActivityDcom/leapiq/braintraining/ui/tests/LearningStyleActivity$WhenMappings;com/leapiq/braintraining/ui/tests/LearningStyleInfoActivity4com/leapiq/braintraining/ui/tests/ProblemSolvingType8com/leapiq/braintraining/ui/tests/ProblemSolvingQuestion6com/leapiq/braintraining/ui/tests/ProblemSolvingOption.com/leapiq/braintraining/ui/tests/QuestionType=com/leapiq/braintraining/ui/tests/ProblemSolvingStyleActivityJcom/leapiq/braintraining/ui/tests/ProblemSolvingStyleActivity$WhenMappingsAcom/leapiq/braintraining/ui/tests/ProblemSolvingStyleInfoActivity4com/leapiq/braintraining/ui/tests/StressResponseType8com/leapiq/braintraining/ui/tests/StressResponseQuestion6com/leapiq/braintraining/ui/tests/StressResponseOption8com/leapiq/braintraining/ui/tests/StressResponseActivityEcom/leapiq/braintraining/ui/tests/StressResponseActivity$WhenMappings<com/leapiq/braintraining/ui/tests/StressResponseInfoActivity/com/leapiq/braintraining/ui/tests/TestsFragment@com/leapiq/braintraining/ui/tests/TestsFragment$setupTestsGrid$15com/leapiq/braintraining/ui/tests/adapter/TestAdapterNcom/leapiq/braintraining/ui/tests/adapter/TestAdapter$Companion$DiffCallback$1Dcom/leapiq/braintraining/ui/tests/adapter/TestAdapter$TestViewHolder?com/leapiq/braintraining/ui/tests/adapter/TestAdapter$CompanionGcom/leapiq/braintraining/ui/tests/adapters/LearningStyleQuestionAdapterXcom/leapiq/braintraining/ui/tests/adapters/LearningStyleQuestionAdapter$OptionViewHolderecom/leapiq/braintraining/ui/tests/adapters/LearningStyleQuestionAdapter$OptionViewHolder$WhenMappings<com/leapiq/braintraining/ui/tests/adapters/MemoryGridAdapterKcom/leapiq/braintraining/ui/tests/adapters/MemoryGridAdapter$GridViewHolderHcom/leapiq/braintraining/ui/tests/adapters/ProblemSolvingQuestionAdapterYcom/leapiq/braintraining/ui/tests/adapters/ProblemSolvingQuestionAdapter$OptionViewHolderfcom/leapiq/braintraining/ui/tests/adapters/ProblemSolvingQuestionAdapter$OptionViewHolder$WhenMappingsHcom/leapiq/braintraining/ui/tests/adapters/StressResponseQuestionAdapterYcom/leapiq/braintraining/ui/tests/adapters/StressResponseQuestionAdapter$OptionViewHolder<com/leapiq/braintraining/ui/tests/results/AllResultsActivityHcom/leapiq/braintraining/ui/tests/results/AllResultsActivity$setupUI$1$2icom/leapiq/braintraining/ui/tests/results/AllResultsActivity$loadAllResults$$inlined$sortedByDescending$18com/leapiq/braintraining/ui/tests/results/TestResultItem;com/leapiq/braintraining/ui/tests/results/AnalyticsActivity;com/leapiq/braintraining/ui/tests/results/MyResultsActivityGcom/leapiq/braintraining/ui/tests/results/MyResultsActivity$setupUI$1$2Gcom/leapiq/braintraining/ui/tests/results/MyResultsActivity$setupUI$1$4Hcom/leapiq/braintraining/ui/tests/results/MyResultsActivity$WhenMappings:com/leapiq/braintraining/ui/tests/results/TestProgressItem>com/leapiq/braintraining/ui/tests/results/TestProgressActivityJcom/leapiq/braintraining/ui/tests/results/TestProgressActivity$setupUI$1$2vcom/leapiq/braintraining/ui/tests/results/TestProgressActivity$loadTestProgress$lambda$6$$inlined$sortedByDescending$1Hcom/leapiq/braintraining/ui/tests/results/TestProgressActivity$CompanionKcom/leapiq/braintraining/ui/tests/results/TestProgressActivity$WhenMappings=com/leapiq/braintraining/ui/tests/results/TestResultsActivity\com/leapiq/braintraining/ui/tests/results/TestResultsActivity$displayInsights$insightsText$1jcom/leapiq/braintraining/ui/tests/results/TestResultsActivity$displayRecommendations$recommendationsText$1Gcom/leapiq/braintraining/ui/tests/results/TestResultsActivity$CompanionDcom/leapiq/braintraining/ui/tests/results/adapters/AllResultsAdapterYcom/leapiq/braintraining/ui/tests/results/adapters/AllResultsAdapter$AllResultsViewHolderfcom/leapiq/braintraining/ui/tests/results/adapters/AllResultsAdapter$AllResultsViewHolder$WhenMappingsQcom/leapiq/braintraining/ui/tests/results/adapters/AllResultsAdapter$DiffCallbackHcom/leapiq/braintraining/ui/tests/results/adapters/DetailedScoresAdapter`com/leapiq/braintraining/ui/tests/results/adapters/DetailedScoresAdapter$DetailedScoreViewHolderHcom/leapiq/braintraining/ui/tests/results/adapters/RecentActivityAdapteracom/leapiq/braintraining/ui/tests/results/adapters/RecentActivityAdapter$RecentActivityViewHolderncom/leapiq/braintraining/ui/tests/results/adapters/RecentActivityAdapter$RecentActivityViewHolder$WhenMappingsUcom/leapiq/braintraining/ui/tests/results/adapters/RecentActivityAdapter$DiffCallbackFcom/leapiq/braintraining/ui/tests/results/adapters/TestProgressAdapter]com/leapiq/braintraining/ui/tests/results/adapters/TestProgressAdapter$TestProgressViewHolderjcom/leapiq/braintraining/ui/tests/results/adapters/TestProgressAdapter$TestProgressViewHolder$WhenMappingsScom/leapiq/braintraining/ui/tests/results/adapters/TestProgressAdapter$DiffCallbackEcom/leapiq/braintraining/ui/tests/results/adapters/TestResultsAdapterZcom/leapiq/braintraining/ui/tests/results/adapters/TestResultsAdapter$TestResultViewHolderRcom/leapiq/braintraining/ui/tests/results/adapters/TestResultsAdapter$DiffCallback/com/leapiq/braintraining/ui/today/TodayFragmentFcom/leapiq/braintraining/ui/today/TodayFragment$setupDailyChallenges$1?com/leapiq/braintraining/ui/today/adapter/DailyChallengeAdapterXcom/leapiq/braintraining/ui/today/adapter/DailyChallengeAdapter$Companion$DiffCallback$1Scom/leapiq/braintraining/ui/today/adapter/DailyChallengeAdapter$ChallengeViewHolderIcom/leapiq/braintraining/ui/today/adapter/DailyChallengeAdapter$Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                