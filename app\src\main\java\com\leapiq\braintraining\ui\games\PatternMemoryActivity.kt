package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityPatternMemoryBinding
import com.leapiq.braintraining.ui.games.adapter.PatternGridAdapter
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date

/**
 * Pattern Memory Game
 * Players watch a grid pattern for a few seconds, then recreate it by clicking cells
 * Difficulty increases with level (larger grids, more complex patterns)
 */
class PatternMemoryActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPatternMemoryBinding
    private lateinit var adapter: PatternGridAdapter
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "memory_3"

    // Pattern game specific
    private var targetPattern = mutableListOf<Boolean>()
    private var playerPattern = mutableListOf<Boolean>()
    private var isShowingPattern = false
    private var isPlayerTurn = false
    private var gridSize = 3

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPatternMemoryBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.pattern_memory)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Watch the pattern, then recreate it!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        gridSize = getGridSize(currentLevel)
        generatePattern()
        setupGrid()
        showPattern()
    }

    private fun getGridSize(level: Int): Int {
        return when (level) {
            in 1..5 -> 3      // 3x3 grid
            in 6..10 -> 4     // 4x4 grid
            in 11..15 -> 5    // 5x5 grid
            in 16..20 -> 6    // 6x6 grid
            else -> 7         // 7x7 grid (max)
        }
    }

    private fun generatePattern() {
        val totalCells = gridSize * gridSize
        targetPattern = MutableList(totalCells) { false }
        playerPattern = MutableList(totalCells) { false }

        // Calculate number of filled cells based on level
        val fillPercentage = when (currentLevel) {
            in 1..5 -> 0.3f    // 30% filled
            in 6..10 -> 0.35f  // 35% filled
            in 11..15 -> 0.4f  // 40% filled
            in 16..20 -> 0.45f // 45% filled
            else -> 0.5f       // 50% filled
        }

        val cellsToFill = (totalCells * fillPercentage).toInt()
        val indicesToFill = (0 until totalCells).shuffled().take(cellsToFill)
        
        indicesToFill.forEach { index ->
            targetPattern[index] = true
        }
    }

    private fun setupGrid() {
        adapter = PatternGridAdapter(
            pattern = playerPattern.toMutableList(),
            gridSize = gridSize,
            isInteractive = false
        ) { position ->
            onCellClicked(position)
        }

        binding.patternGrid.apply {
            layoutManager = GridLayoutManager(this@PatternMemoryActivity, gridSize)
            adapter = <EMAIL>
        }
    }

    private fun showPattern() {
        isShowingPattern = true
        isPlayerTurn = false
        
        binding.instructionText.text = "Memorize this pattern..."
        
        // Show the target pattern
        adapter.updatePattern(targetPattern, false)
        
        // Hide pattern after delay
        val showDuration = getShowDuration(currentLevel)
        Handler(Looper.getMainLooper()).postDelayed({
            hidePattern()
        }, showDuration)
    }

    private fun getShowDuration(level: Int): Long {
        return when (level) {
            in 1..5 -> 3000L    // 3 seconds
            in 6..10 -> 2500L   // 2.5 seconds
            in 11..15 -> 2000L  // 2 seconds
            in 16..20 -> 1500L  // 1.5 seconds
            else -> 1000L       // 1 second
        }
    }

    private fun hidePattern() {
        isShowingPattern = false
        isPlayerTurn = true
        
        binding.instructionText.text = "Now recreate the pattern!"
        
        // Clear the grid and make it interactive
        playerPattern = MutableList(gridSize * gridSize) { false }
        adapter.updatePattern(playerPattern, true)
    }

    private fun onCellClicked(position: Int) {
        if (!isPlayerTurn) return

        // Toggle cell state
        playerPattern[position] = !playerPattern[position]
        adapter.updatePattern(playerPattern, true)
        
        // Check if player wants to submit (could add a submit button, but for now auto-check)
        // For simplicity, we'll check when they've filled the expected number of cells
        val filledCells = playerPattern.count { it }
        val targetFilledCells = targetPattern.count { it }
        
        if (filledCells == targetFilledCells) {
            // Auto-submit when they've filled the right number of cells
            Handler(Looper.getMainLooper()).postDelayed({
                checkPattern()
            }, 500) // Small delay to show the last click
        }
    }

    private fun checkPattern() {
        isPlayerTurn = false
        
        val isCorrect = targetPattern == playerPattern
        totalAttempts++
        
        if (isCorrect) {
            totalCorrect++
            binding.instructionText.text = "Perfect! Pattern matched!"
            
            // Show correct pattern briefly
            adapter.updatePattern(targetPattern, false)
            
            Handler(Looper.getMainLooper()).postDelayed({
                roundComplete()
            }, 1500)
        } else {
            binding.instructionText.text = "Not quite right. Here was the correct pattern:"
            
            // Show correct pattern
            adapter.updatePattern(targetPattern, false)
            
            Handler(Looper.getMainLooper()).postDelayed({
                roundFailed()
            }, 2500)
        }
    }

    private fun roundComplete() {
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = "Round $currentRound starting..."
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            Handler(Looper.getMainLooper()).postDelayed({
                setupGame()
            }, 1500)
        }
    }

    private fun roundFailed() {
        binding.instructionText.text = "Try again! Round $currentRound starting..."
        
        Handler(Looper.getMainLooper()).postDelayed({
            setupGame()
        }, 1500)
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = i <= totalCorrect,
                timeSpentMs = totalTime / maxRounds,
                attempts = 1
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Time: ${totalTime / 1000}s
                Grid Size: ${gridSize}x${gridSize}

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Time: ${totalTime / 1000}s

                Pattern Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Memorize and recreate visual patterns

                📋 RULES:
                • Watch the pattern shown on the grid
                • Memorize which cells are filled
                • Recreate the pattern by tapping cells
                • Complete 3 rounds to advance

                💡 TIPS:
                • Focus on the pattern shape
                • Look for recognizable forms
                • Practice visual memory techniques

                🏆 SCORING:
                • Accuracy = correct patterns / total attempts
                • Faster completion = better score
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
