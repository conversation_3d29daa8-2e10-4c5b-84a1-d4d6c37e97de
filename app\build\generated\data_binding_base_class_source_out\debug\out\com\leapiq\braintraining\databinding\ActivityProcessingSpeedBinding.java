// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProcessingSpeedBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button blueButton;

  @NonNull
  public final LinearLayout choiceReactionContainer;

  @NonNull
  public final TextView choiceStimulus;

  @NonNull
  public final LinearLayout decisionSpeedContainer;

  @NonNull
  public final TextView decisionText;

  @NonNull
  public final TextView feedbackText;

  @NonNull
  public final Button greenButton;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final Button noButton;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final Button redButton;

  @NonNull
  public final LinearLayout simpleReactionContainer;

  @NonNull
  public final Button simpleResponseButton;

  @NonNull
  public final TextView stimulusDisplay;

  @NonNull
  public final Button symbol1Button;

  @NonNull
  public final Button symbol2Button;

  @NonNull
  public final Button symbol3Button;

  @NonNull
  public final Button symbol4Button;

  @NonNull
  public final LinearLayout symbolCodingContainer;

  @NonNull
  public final TextView symbolDisplay;

  @NonNull
  public final TextView symbolMappingText;

  @NonNull
  public final TextView taskTypeText;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final Button yellowButton;

  @NonNull
  public final Button yesButton;

  private ActivityProcessingSpeedBinding(@NonNull LinearLayout rootView, @NonNull Button blueButton,
      @NonNull LinearLayout choiceReactionContainer, @NonNull TextView choiceStimulus,
      @NonNull LinearLayout decisionSpeedContainer, @NonNull TextView decisionText,
      @NonNull TextView feedbackText, @NonNull Button greenButton,
      @NonNull TextView instructionText, @NonNull Button noButton, @NonNull ProgressBar progressBar,
      @NonNull TextView progressText, @NonNull Button redButton,
      @NonNull LinearLayout simpleReactionContainer, @NonNull Button simpleResponseButton,
      @NonNull TextView stimulusDisplay, @NonNull Button symbol1Button,
      @NonNull Button symbol2Button, @NonNull Button symbol3Button, @NonNull Button symbol4Button,
      @NonNull LinearLayout symbolCodingContainer, @NonNull TextView symbolDisplay,
      @NonNull TextView symbolMappingText, @NonNull TextView taskTypeText, @NonNull Toolbar toolbar,
      @NonNull Button yellowButton, @NonNull Button yesButton) {
    this.rootView = rootView;
    this.blueButton = blueButton;
    this.choiceReactionContainer = choiceReactionContainer;
    this.choiceStimulus = choiceStimulus;
    this.decisionSpeedContainer = decisionSpeedContainer;
    this.decisionText = decisionText;
    this.feedbackText = feedbackText;
    this.greenButton = greenButton;
    this.instructionText = instructionText;
    this.noButton = noButton;
    this.progressBar = progressBar;
    this.progressText = progressText;
    this.redButton = redButton;
    this.simpleReactionContainer = simpleReactionContainer;
    this.simpleResponseButton = simpleResponseButton;
    this.stimulusDisplay = stimulusDisplay;
    this.symbol1Button = symbol1Button;
    this.symbol2Button = symbol2Button;
    this.symbol3Button = symbol3Button;
    this.symbol4Button = symbol4Button;
    this.symbolCodingContainer = symbolCodingContainer;
    this.symbolDisplay = symbolDisplay;
    this.symbolMappingText = symbolMappingText;
    this.taskTypeText = taskTypeText;
    this.toolbar = toolbar;
    this.yellowButton = yellowButton;
    this.yesButton = yesButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProcessingSpeedBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProcessingSpeedBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_processing_speed, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProcessingSpeedBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.blue_button;
      Button blueButton = ViewBindings.findChildViewById(rootView, id);
      if (blueButton == null) {
        break missingId;
      }

      id = R.id.choice_reaction_container;
      LinearLayout choiceReactionContainer = ViewBindings.findChildViewById(rootView, id);
      if (choiceReactionContainer == null) {
        break missingId;
      }

      id = R.id.choice_stimulus;
      TextView choiceStimulus = ViewBindings.findChildViewById(rootView, id);
      if (choiceStimulus == null) {
        break missingId;
      }

      id = R.id.decision_speed_container;
      LinearLayout decisionSpeedContainer = ViewBindings.findChildViewById(rootView, id);
      if (decisionSpeedContainer == null) {
        break missingId;
      }

      id = R.id.decision_text;
      TextView decisionText = ViewBindings.findChildViewById(rootView, id);
      if (decisionText == null) {
        break missingId;
      }

      id = R.id.feedback_text;
      TextView feedbackText = ViewBindings.findChildViewById(rootView, id);
      if (feedbackText == null) {
        break missingId;
      }

      id = R.id.green_button;
      Button greenButton = ViewBindings.findChildViewById(rootView, id);
      if (greenButton == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.no_button;
      Button noButton = ViewBindings.findChildViewById(rootView, id);
      if (noButton == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progress_text;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.red_button;
      Button redButton = ViewBindings.findChildViewById(rootView, id);
      if (redButton == null) {
        break missingId;
      }

      id = R.id.simple_reaction_container;
      LinearLayout simpleReactionContainer = ViewBindings.findChildViewById(rootView, id);
      if (simpleReactionContainer == null) {
        break missingId;
      }

      id = R.id.simple_response_button;
      Button simpleResponseButton = ViewBindings.findChildViewById(rootView, id);
      if (simpleResponseButton == null) {
        break missingId;
      }

      id = R.id.stimulus_display;
      TextView stimulusDisplay = ViewBindings.findChildViewById(rootView, id);
      if (stimulusDisplay == null) {
        break missingId;
      }

      id = R.id.symbol_1_button;
      Button symbol1Button = ViewBindings.findChildViewById(rootView, id);
      if (symbol1Button == null) {
        break missingId;
      }

      id = R.id.symbol_2_button;
      Button symbol2Button = ViewBindings.findChildViewById(rootView, id);
      if (symbol2Button == null) {
        break missingId;
      }

      id = R.id.symbol_3_button;
      Button symbol3Button = ViewBindings.findChildViewById(rootView, id);
      if (symbol3Button == null) {
        break missingId;
      }

      id = R.id.symbol_4_button;
      Button symbol4Button = ViewBindings.findChildViewById(rootView, id);
      if (symbol4Button == null) {
        break missingId;
      }

      id = R.id.symbol_coding_container;
      LinearLayout symbolCodingContainer = ViewBindings.findChildViewById(rootView, id);
      if (symbolCodingContainer == null) {
        break missingId;
      }

      id = R.id.symbol_display;
      TextView symbolDisplay = ViewBindings.findChildViewById(rootView, id);
      if (symbolDisplay == null) {
        break missingId;
      }

      id = R.id.symbol_mapping_text;
      TextView symbolMappingText = ViewBindings.findChildViewById(rootView, id);
      if (symbolMappingText == null) {
        break missingId;
      }

      id = R.id.task_type_text;
      TextView taskTypeText = ViewBindings.findChildViewById(rootView, id);
      if (taskTypeText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.yellow_button;
      Button yellowButton = ViewBindings.findChildViewById(rootView, id);
      if (yellowButton == null) {
        break missingId;
      }

      id = R.id.yes_button;
      Button yesButton = ViewBindings.findChildViewById(rootView, id);
      if (yesButton == null) {
        break missingId;
      }

      return new ActivityProcessingSpeedBinding((LinearLayout) rootView, blueButton,
          choiceReactionContainer, choiceStimulus, decisionSpeedContainer, decisionText,
          feedbackText, greenButton, instructionText, noButton, progressBar, progressText,
          redButton, simpleReactionContainer, simpleResponseButton, stimulusDisplay, symbol1Button,
          symbol2Button, symbol3Button, symbol4Button, symbolCodingContainer, symbolDisplay,
          symbolMappingText, taskTypeText, toolbar, yellowButton, yesButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
