-- Merging decision tree log ---
manifest
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:2:1-211:12
INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:2:1-211:12
INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:2:1-211:12
INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:2:1-211:12
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad5ee37786fe4a95d286780266d46963\transformed\jetified-viewbinding-8.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3521d16f93f9a94f984413b5f2824541\transformed\navigation-common-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c177fdf5e2588568e092f4d796f675f8\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a464d1464171ec16cf6bc43edada8c6\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6105e1bc52b943ca73e8ebaeb913c84\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d737e9f6d85820102d92b666ded10c35\transformed\navigation-fragment-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\192d7c879c5a4233f80f7e9281d4a385\transformed\navigation-fragment-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c46b9ee4b603241d905f420dafb61674\transformed\navigation-ui-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c81b9c5de38cf196c956f561c1361c\transformed\navigation-ui-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\483818d0c523612987cfb44bb19710ad\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\626a7c2d5f7ff375f49495470c2cae7b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04a815f8436fd91623eecd63f0a69eac\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5eef04db0f25a1c94c01cf88ad6b7047\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6786b286a6e1e4c262109c306cf3cf\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cb8f11c0f0d1882b4173a453a4c6ea5\transformed\fragment-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bea9d7351ec6e9186e5bf3362a2a1a7\transformed\jetified-fragment-ktx-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4abf5513fa42381d29d62b5cec68875\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b9470012172124c420268bf6b7a7f84\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d39c5d5caf74a714af0ac9b2332eea1a\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a1ec3e02c0f1419d6257592a160b473\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4638670b772d5b3efd46b4c9f1f547b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d425fc1a76588be9fe1e17da56f47bd\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b29f856ca96e3a04efaa0e40c2c20bb1\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96d27300e174b763b52c7814cf2f8be1\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\247d76f46abea158c21020c0f16e6944\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5534d3ac61a6966dfb08359041a964a3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2cd5af2162080a479bec0bc728cca735\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2304698a1b88654794bbc107ef1aadae\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b65d72352b4229dd97266fc14ce07aca\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf6eaaf0a804608b4d007404a9330ef7\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf53a6eb7db71e141a55aaa895a75\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41dbc14474e5a4d8a1719a5e5ee567ae\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c48b5fdd26ec51e7ab0f640e6a98229\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00484afb2114a6ea9e1fa907c191ed6d\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdea4b7e70873bf47c26ff8681fcb9f\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e99eebdcefd9f1f74de827b52a3742c6\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1b8bfeede101ad959011185090afc87\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d772f56f1e916accbabf78f91ffceb0a\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0420f09f93d134ff986d8844252bd41\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\49301af1a0e06575359e7a50fc558461\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d33188d6d1c393c320eb00cd4fa249\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff37346ac1aa81a2288f11f364c1eed6\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9b6fa52ae22789aede18bb95f6cb0a7\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\084b7bd0f856804efc259adc108aa935\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5f0f86e185ed2fd394571ae032022c\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8565220236b5d5cc48f0160e738254a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51466649e55ec65d1b7f3fcd1b3c46d8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff9787069bcf3651c3c22aebc8936c45\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cdb10a1b3b65e6785d0a695b58ca309b\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\545fc0883731112413511df2493466bd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\457f52ce828c3fbc4952a6b12be437e4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fd5ea8875ca4b13a55e6f8ac27b67a5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed04bf4ba5814d5c8a84f2e66385109\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9851408d7f4ef78ffd32544e60acc5\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef2de46a971d4c138534acf1992a325c\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:5:5-209:19
INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:5:5-209:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\483818d0c523612987cfb44bb19710ad\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\483818d0c523612987cfb44bb19710ad\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\626a7c2d5f7ff375f49495470c2cae7b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\626a7c2d5f7ff375f49495470c2cae7b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8565220236b5d5cc48f0160e738254a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8565220236b5d5cc48f0160e738254a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\545fc0883731112413511df2493466bd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\545fc0883731112413511df2493466bd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:11:9-47
	tools:targetApi
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:14:9-29
	android:icon
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:9:9-42
	android:allowBackup
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:13:9-44
	android:dataExtractionRules
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:7:9-65
activity#com.leapiq.braintraining.MainActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:16:9-24:20
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:18:13-36
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:19:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:17:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:20:13-23:29
action#android.intent.action.MAIN
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:22:17-77
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:22:27-74
activity#com.leapiq.braintraining.ui.games.CardMatchingActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:27:9-31:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:31:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:30:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:28:13-58
activity#com.leapiq.braintraining.ui.games.SpeedMathActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:33:9-37:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:37:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:35:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:36:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:34:13-55
activity#com.leapiq.braintraining.ui.games.StroopTestActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:39:9-43:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:43:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:41:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:42:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:40:13-56
activity#com.leapiq.braintraining.ui.games.VocabularyActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:45:9-49:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:49:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:47:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:48:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:46:13-56
activity#com.leapiq.braintraining.ui.games.WordSearchActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:51:9-55:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:55:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:53:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:54:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:52:13-56
activity#com.leapiq.braintraining.ui.games.SequenceRecallActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:58:9-62:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:62:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:60:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:61:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:59:13-60
activity#com.leapiq.braintraining.ui.games.PatternMemoryActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:64:9-68:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:68:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:66:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:67:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:65:13-59
activity#com.leapiq.braintraining.ui.games.NumberMemoryActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:70:9-74:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:74:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:73:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:71:13-58
activity#com.leapiq.braintraining.ui.games.ReactionTimeActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:77:9-81:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:81:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:79:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:80:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:78:13-58
activity#com.leapiq.braintraining.ui.games.VisualSearchActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:83:9-87:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:87:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:85:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:86:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:84:13-58
activity#com.leapiq.braintraining.ui.games.FocusChallengeActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:89:9-93:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:93:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:91:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:92:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:90:13-60
activity#com.leapiq.braintraining.ui.games.MentalArithmeticActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:96:9-100:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:100:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:98:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:99:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:97:13-62
activity#com.leapiq.braintraining.ui.games.NumberSequencesActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:102:9-106:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:106:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:104:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:105:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:103:13-61
activity#com.leapiq.braintraining.ui.games.EstimationActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:108:9-112:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:112:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:110:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:111:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:109:13-56
activity#com.leapiq.braintraining.ui.games.TubeSortActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:115:9-119:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:119:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:117:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:118:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:116:13-54
activity#com.leapiq.braintraining.ui.games.LogicalReasoningActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:121:9-125:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:125:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:123:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:124:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:122:13-62
activity#com.leapiq.braintraining.ui.games.SpatialRotationActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:127:9-131:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:131:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:129:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:130:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:128:13-61
activity#com.leapiq.braintraining.ui.games.TowerOfHanoiActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:133:9-137:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:137:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:135:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:136:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:134:13-58
activity#com.leapiq.braintraining.ui.games.PatternCompletionActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:139:9-143:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:143:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:141:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:142:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:140:13-63
activity#com.leapiq.braintraining.ui.games.WordAssociationActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:146:9-150:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:150:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:148:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:149:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:147:13-61
activity#com.leapiq.braintraining.ui.games.AnagramsActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:152:9-156:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:156:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:154:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:155:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:153:13-54
activity#com.leapiq.braintraining.ui.games.LogicPuzzlesActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:158:9-162:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:162:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:160:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:161:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:159:13-58
activity#com.leapiq.braintraining.ui.games.GameResultActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:164:9-168:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:168:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:166:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:167:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:165:13-56
activity#com.leapiq.braintraining.ui.tests.LearningStyleActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:173:9-177:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:177:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:175:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:176:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:174:13-59
activity#com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:179:9-183:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:183:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:181:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:182:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:180:13-65
activity#com.leapiq.braintraining.ui.tests.StressResponseActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:185:9-189:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:189:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:187:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:188:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:186:13-60
activity#com.leapiq.braintraining.ui.tests.results.TestResultsActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:191:9-195:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:195:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:193:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:194:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:192:13-65
activity#com.leapiq.braintraining.ui.tests.results.AllResultsActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:197:9-201:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:201:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:199:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:200:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:198:13-64
activity#com.leapiq.braintraining.ui.tests.results.MyResultsActivity
ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:203:9-207:52
	android:screenOrientation
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:207:13-49
	android:exported
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:205:13-37
	android:theme
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:206:13-48
	android:name
		ADDED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:204:13-63
uses-sdk
INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml
INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad5ee37786fe4a95d286780266d46963\transformed\jetified-viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad5ee37786fe4a95d286780266d46963\transformed\jetified-viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3521d16f93f9a94f984413b5f2824541\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3521d16f93f9a94f984413b5f2824541\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c177fdf5e2588568e092f4d796f675f8\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c177fdf5e2588568e092f4d796f675f8\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a464d1464171ec16cf6bc43edada8c6\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a464d1464171ec16cf6bc43edada8c6\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6105e1bc52b943ca73e8ebaeb913c84\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6105e1bc52b943ca73e8ebaeb913c84\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d737e9f6d85820102d92b666ded10c35\transformed\navigation-fragment-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d737e9f6d85820102d92b666ded10c35\transformed\navigation-fragment-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\192d7c879c5a4233f80f7e9281d4a385\transformed\navigation-fragment-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\192d7c879c5a4233f80f7e9281d4a385\transformed\navigation-fragment-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c46b9ee4b603241d905f420dafb61674\transformed\navigation-ui-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c46b9ee4b603241d905f420dafb61674\transformed\navigation-ui-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c81b9c5de38cf196c956f561c1361c\transformed\navigation-ui-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c81b9c5de38cf196c956f561c1361c\transformed\navigation-ui-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\483818d0c523612987cfb44bb19710ad\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\483818d0c523612987cfb44bb19710ad\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\626a7c2d5f7ff375f49495470c2cae7b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\626a7c2d5f7ff375f49495470c2cae7b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04a815f8436fd91623eecd63f0a69eac\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04a815f8436fd91623eecd63f0a69eac\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5eef04db0f25a1c94c01cf88ad6b7047\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5eef04db0f25a1c94c01cf88ad6b7047\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6786b286a6e1e4c262109c306cf3cf\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6786b286a6e1e4c262109c306cf3cf\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cb8f11c0f0d1882b4173a453a4c6ea5\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cb8f11c0f0d1882b4173a453a4c6ea5\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bea9d7351ec6e9186e5bf3362a2a1a7\transformed\jetified-fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bea9d7351ec6e9186e5bf3362a2a1a7\transformed\jetified-fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4abf5513fa42381d29d62b5cec68875\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4abf5513fa42381d29d62b5cec68875\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b9470012172124c420268bf6b7a7f84\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b9470012172124c420268bf6b7a7f84\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d39c5d5caf74a714af0ac9b2332eea1a\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d39c5d5caf74a714af0ac9b2332eea1a\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a1ec3e02c0f1419d6257592a160b473\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a1ec3e02c0f1419d6257592a160b473\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4638670b772d5b3efd46b4c9f1f547b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4638670b772d5b3efd46b4c9f1f547b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d425fc1a76588be9fe1e17da56f47bd\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d425fc1a76588be9fe1e17da56f47bd\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b29f856ca96e3a04efaa0e40c2c20bb1\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b29f856ca96e3a04efaa0e40c2c20bb1\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96d27300e174b763b52c7814cf2f8be1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96d27300e174b763b52c7814cf2f8be1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\247d76f46abea158c21020c0f16e6944\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\247d76f46abea158c21020c0f16e6944\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5534d3ac61a6966dfb08359041a964a3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5534d3ac61a6966dfb08359041a964a3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2cd5af2162080a479bec0bc728cca735\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2cd5af2162080a479bec0bc728cca735\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2304698a1b88654794bbc107ef1aadae\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2304698a1b88654794bbc107ef1aadae\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b65d72352b4229dd97266fc14ce07aca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b65d72352b4229dd97266fc14ce07aca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf6eaaf0a804608b4d007404a9330ef7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf6eaaf0a804608b4d007404a9330ef7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf53a6eb7db71e141a55aaa895a75\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf53a6eb7db71e141a55aaa895a75\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41dbc14474e5a4d8a1719a5e5ee567ae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41dbc14474e5a4d8a1719a5e5ee567ae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c48b5fdd26ec51e7ab0f640e6a98229\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c48b5fdd26ec51e7ab0f640e6a98229\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00484afb2114a6ea9e1fa907c191ed6d\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00484afb2114a6ea9e1fa907c191ed6d\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdea4b7e70873bf47c26ff8681fcb9f\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdea4b7e70873bf47c26ff8681fcb9f\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e99eebdcefd9f1f74de827b52a3742c6\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e99eebdcefd9f1f74de827b52a3742c6\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1b8bfeede101ad959011185090afc87\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1b8bfeede101ad959011185090afc87\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d772f56f1e916accbabf78f91ffceb0a\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d772f56f1e916accbabf78f91ffceb0a\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0420f09f93d134ff986d8844252bd41\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0420f09f93d134ff986d8844252bd41\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\49301af1a0e06575359e7a50fc558461\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\49301af1a0e06575359e7a50fc558461\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d33188d6d1c393c320eb00cd4fa249\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d33188d6d1c393c320eb00cd4fa249\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff37346ac1aa81a2288f11f364c1eed6\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff37346ac1aa81a2288f11f364c1eed6\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9b6fa52ae22789aede18bb95f6cb0a7\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9b6fa52ae22789aede18bb95f6cb0a7\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\084b7bd0f856804efc259adc108aa935\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\084b7bd0f856804efc259adc108aa935\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5f0f86e185ed2fd394571ae032022c\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5f0f86e185ed2fd394571ae032022c\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8565220236b5d5cc48f0160e738254a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8565220236b5d5cc48f0160e738254a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51466649e55ec65d1b7f3fcd1b3c46d8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51466649e55ec65d1b7f3fcd1b3c46d8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff9787069bcf3651c3c22aebc8936c45\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff9787069bcf3651c3c22aebc8936c45\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cdb10a1b3b65e6785d0a695b58ca309b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cdb10a1b3b65e6785d0a695b58ca309b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\545fc0883731112413511df2493466bd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\545fc0883731112413511df2493466bd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\457f52ce828c3fbc4952a6b12be437e4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\457f52ce828c3fbc4952a6b12be437e4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fd5ea8875ca4b13a55e6f8ac27b67a5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fd5ea8875ca4b13a55e6f8ac27b67a5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed04bf4ba5814d5c8a84f2e66385109\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed04bf4ba5814d5c8a84f2e66385109\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9851408d7f4ef78ffd32544e60acc5\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9851408d7f4ef78ffd32544e60acc5\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef2de46a971d4c138534acf1992a325c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef2de46a971d4c138534acf1992a325c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\545fc0883731112413511df2493466bd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\545fc0883731112413511df2493466bd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
