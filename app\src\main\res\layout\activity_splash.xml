<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@drawable/splash_background"
    android:padding="32dp">

    <!-- App Icon -->
    <ImageView
        android:id="@+id/app_icon"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@mipmap/ic_launcher"
        android:layout_marginBottom="24dp"
        android:elevation="8dp" />

    <!-- App Name -->
    <TextView
        android:id="@+id/app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/text_white"
        android:layout_marginBottom="8dp"
        android:fontFamily="sans-serif-medium" />

    <!-- Tagline -->
    <TextView
        android:id="@+id/tagline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Train Your Brain"
        android:textSize="16sp"
        android:textColor="@color/text_white"
        android:alpha="0.9"
        android:fontFamily="sans-serif-light" />

    <!-- Loading indicator (optional) -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <ProgressBar
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:indeterminateTint="@color/text_white"
        android:layout_marginBottom="32dp"
        android:alpha="0.7" />

</LinearLayout>
