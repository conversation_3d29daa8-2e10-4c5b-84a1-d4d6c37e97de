O$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\Ball.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\ReactionTimeActivity.ktT$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestProgressManager.kt]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicalReasoningActivity.kt^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\HanoiTowerAdapter.ktU$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\BaseTestActivity.ktH$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\MainActivity.kt`$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestResultsActivity.ktX$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\TubeAdapter.kt[$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\FocusChallengeActivity.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\BaseTestInfoActivity.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\CluesAdapter.ktU$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\HanoiTower.kt\$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\WordAssociationActivity.kt\$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpatialRotationActivity.ktW$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\EstimationActivity.ktV$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpeedMathActivity.kt[$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SequenceRecallActivity.ktV$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestDataBackupManager.ktb$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\adapter\DailyChallengeAdapter.ktj$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\LearningStyleQuestionAdapter.kt^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\PatternCompletionActivity.ktU$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\DailyChallenge.ktk$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\RecentActivityAdapter.ktX$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapter\TestAdapter.ktW$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\WordSearchActivity.ktb$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordSearchGridAdapter.kt]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\LogicGridAdapter.kth$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\TestResultsAdapter.ktg$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\AllResultsAdapter.ktk$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\DetailedScoresAdapter.ktW$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\StroopTestActivity.kt`$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.ktR$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\GamesFragment.ktg$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\CategoryAccuracyAdapter.ktW$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.kta$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktT$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\GameProgressManager.ktX$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\GameAdapter.ktK$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\Test.ktU$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\TubeSortActivity.kt_$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\MemoryGridAdapter.ktW$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\GameResultActivity.kt`$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\VisualSearchAdapter.kt[$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.ktk$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\ProblemSolvingQuestionAdapter.ktZ$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\PatternMemoryActivity.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VisualSearchActivity.kt^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleInfoActivity.ktQ$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\GameResult.kt^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.ktR$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\TestsFragment.kt_$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\PatternGridAdapter.kt_$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AllResultsActivity.kti$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\TestProgressAdapter.ktU$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\AnagramsActivity.ktW$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\analysis\TestAnalysisEngine.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicPuzzlesActivity.ktR$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\TodayFragment.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\CardMatchingActivity.kt_$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseInfoActivity.ktX$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\ProgressFragment.ktT$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\HanoiDisk.kt^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AnalyticsActivity.ktV$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCard.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\WordSearchWord.ktV$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\LogicPuzzle.ktO$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\Tube.ktR$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\Achievement.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\WordSearchGrid.ktV$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\profile\ProfileFragment.ktd$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleInfoActivity.ktK$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\Game.ktb$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\AchievementAdapter.kt]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCardAdapter.ktV$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.kt\$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\NumberSequencesActivity.ktk$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\StressResponseQuestionAdapter.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\TowerOfHanoiActivity.ktU$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\BaseGameActivity.ktU$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\SearchItem.kt]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\MentalArithmeticActivity.ktZ$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt\$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordListAdapter.ktY$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\NumberMemoryActivity.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    