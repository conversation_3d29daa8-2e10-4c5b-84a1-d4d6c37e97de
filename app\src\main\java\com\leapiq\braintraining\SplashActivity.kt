package com.leapiq.braintraining

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.databinding.ActivitySplashBinding

class SplashActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivitySplashBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Hide status bar for full screen splash
        window.statusBarColor = getColor(R.color.primary_light_blue)
        
        // Start app icon animation
        startIconAnimation()
        
        // Navigate to main activity after delay
        Handler(Looper.getMainLooper()).postDelayed({
            startActivity(Intent(this, MainActivity::class.java))
            finish()
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
        }, 2500) // 2.5 seconds
    }
    
    private fun startIconAnimation() {
        // Scale animation for the app icon
        binding.appIcon.scaleX = 0.3f
        binding.appIcon.scaleY = 0.3f
        binding.appIcon.alpha = 0.0f
        
        binding.appIcon.animate()
            .scaleX(1.0f)
            .scaleY(1.0f)
            .alpha(1.0f)
            .setDuration(800)
            .setStartDelay(300)
            .start()
            
        // Animate app name
        binding.appName.alpha = 0.0f
        binding.appName.translationY = 50f
        
        binding.appName.animate()
            .alpha(1.0f)
            .translationY(0f)
            .setDuration(600)
            .setStartDelay(800)
            .start()
            
        // Animate tagline
        binding.tagline.alpha = 0.0f
        binding.tagline.translationY = 30f
        
        binding.tagline.animate()
            .alpha(1.0f)
            .translationY(0f)
            .setDuration(500)
            .setStartDelay(1200)
            .start()
    }
}
