package com.leapiq.braintraining.ui.games.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R

/**
 * Adapter for the pattern memory grid
 * Displays a grid of cells that can be filled or empty
 */
class PatternGridAdapter(
    private var pattern: MutableList<Boolean>,
    private val gridSize: Int,
    private var isInteractive: Boolean,
    private val onCellClicked: (Int) -> Unit
) : RecyclerView.Adapter<PatternGridAdapter.PatternCellViewHolder>() {

    class PatternCellViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val cellView: View = itemView.findViewById(R.id.pattern_cell)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatternCellViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_pattern_cell, parent, false)
        return PatternCellViewHolder(view)
    }

    override fun onBindViewHolder(holder: PatternCellViewHolder, position: Int) {
        val isFilled = pattern[position]
        
        // Set cell appearance based on state
        if (isFilled) {
            holder.cellView.setBackgroundResource(R.drawable.pattern_cell_filled)
        } else {
            holder.cellView.setBackgroundResource(R.drawable.pattern_cell_empty)
        }

        // Set click listener if interactive
        if (isInteractive) {
            holder.cellView.setOnClickListener {
                onCellClicked(position)
            }
            holder.cellView.alpha = 1.0f
        } else {
            holder.cellView.setOnClickListener(null)
            holder.cellView.alpha = 0.9f
        }
    }

    override fun getItemCount() = pattern.size

    /**
     * Update the pattern and interaction state
     */
    fun updatePattern(newPattern: MutableList<Boolean>, interactive: Boolean) {
        pattern = newPattern
        isInteractive = interactive
        notifyDataSetChanged()
    }
}
