[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_logic_grid_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_logic_grid_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_game_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\game_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_disk_yellow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_disk_yellow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_logic_grid_row.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_logic_grid_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_disk_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_disk_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_search_shape_square.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\search_shape_square.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_recent_activity.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_recent_activity.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_tower_base.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_tower_base.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_memory_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_memory_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_word_search_cell.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_word_search_cell.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_number_memory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_number_memory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_reaction_stimulus_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\reaction_stimulus_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_rounded_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\rounded_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_disk_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_disk_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_sequence_button_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\sequence_button_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_focus_challenge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_focus_challenge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_disk_orange.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_disk_orange.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_favorite.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_favorite.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_disk_pink.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_disk_pink.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_learning_style_option.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_learning_style_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_all_results.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_all_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_estimation_answer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\estimation_answer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_speed_math.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_speed_math.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_word_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_word_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_games.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_games.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_test_result.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_test_result.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_logical_reasoning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\logical_reasoning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_sequence_button_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\sequence_button_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_sports.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_sports.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_button_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\button_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_memory_grid_pattern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\memory_grid_pattern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_word_search_word.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_word_search_word.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_test_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_test_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_fragment_games.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_games.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_heart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_heart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_square.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_square.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_analytics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_analytics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_tests.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_tests.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_game_result.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_game_result.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_game_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_game_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_pattern_display_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\pattern_display_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_focus_stimulus_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\focus_stimulus_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_reaction_time.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_reaction_time.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\menu_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\menu\\bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_analytics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_analytics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tower_of_hanoi.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tower_of_hanoi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_logic_question_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\logic_question_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_memory_grid.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_memory_grid.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_number_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\number_input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_ball_purple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_ball_purple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_disk_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_disk_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_test_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_test_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_my_results.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_my_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_stroop_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_stroop_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_detailed_score.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_detailed_score.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_hanoi_tower.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_hanoi_tower.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_tower_selected_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_tower_selected_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_pattern_completion.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_pattern_completion.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_spatial_shape_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\spatial_shape_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_memory_grid_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\memory_grid_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_sequence_button_yellow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\sequence_button_yellow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_estimation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_estimation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_normal_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_normal_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_disk_cyan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_disk_cyan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_trophy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_trophy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_number_display_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\number_display_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_search_shape_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\search_shape_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_search_shape_diamond.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\search_shape_diamond.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_vocabulary_prompt_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\vocabulary_prompt_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_fragment_tests.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_tests.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_speed_math_answer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\speed_math_answer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_button_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\button_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_all_results.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_all_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_sequence_recall.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_sequence_recall.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_pets.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_pets.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_lock.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_lock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_fragment_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_tower_of_hanoi.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_tower_of_hanoi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_word_search_grid_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\word_search_grid_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_pattern_cell_empty.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\pattern_cell_empty.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_ball_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_ball_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_ball_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_ball_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_logic_premises_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\logic_premises_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_word_association.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_word_association.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_problem_solving_option.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_problem_solving_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_attention_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_attention_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_memory_grid_highlighted.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\memory_grid_highlighted.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_button_danger.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\button_danger.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_sequence_display_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\sequence_display_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_apple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_apple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_tube_sort.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_tube_sort.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_tower_normal_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_tower_normal_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_search_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_search_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_math_answer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\math_answer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_star.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_star.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_car.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_car.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_anagram_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\anagram_input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_anagrams.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_anagrams.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_ball_orange.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_ball_orange.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_number_sequences.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_number_sequences.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_pattern_question_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\pattern_question_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_achievement.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_achievement.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_pattern_cell_filled.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\pattern_cell_filled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_triangle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_triangle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_vocabulary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_vocabulary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_word_search_cell_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\word_search_cell_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_dialog_game_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\dialog_game_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_pattern_cell.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_pattern_cell.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_word_search_list_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\word_search_list_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_spatial_rotation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\spatial_rotation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_disk_purple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_disk_purple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_ball_cyan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_ball_cyan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_empty_slot.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_empty_slot.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_sequence_button_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\sequence_button_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_learning_style.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_learning_style.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_estimation_display_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\estimation_display_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_ball_yellow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_ball_yellow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_sequence_answer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\sequence_answer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_problem_solving_style.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_problem_solving_style.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\navigation_nav_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\navigation\\nav_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_speed_math_problem_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\speed_math_problem_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_category_accuracy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_category_accuracy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_pattern_memory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_pattern_memory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_music.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_music.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\anim_fade_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\anim\\fade_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_word_target_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\word_target_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_memory_assessment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_memory_assessment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_card_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_card_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_logical_reasoning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_logical_reasoning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_diamond.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_diamond.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_card_matching.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_card_matching.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_ball_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_ball_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_logic_clue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_logic_clue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_mental_arithmetic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_mental_arithmetic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_ball_pink.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_ball_pink.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_stress_response.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_stress_response.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_search_shape_triangle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\search_shape_triangle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_school.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_school.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_processing_speed.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_processing_speed.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_premium_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\premium_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_stress_response_option.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_stress_response_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_stroop_word_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\stroop_word_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_anagram_scrambled_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\anagram_scrambled_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_logic_puzzles.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_logic_puzzles.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_tube.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_tube.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_hanoi_tower_peg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\hanoi_tower_peg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_memory_grid_default.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\memory_grid_default.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_tube_selected_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\tube_selected_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_today.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_today.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_header_icon_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\header_icon_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_ic_streak.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\ic_streak.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_test_results.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_test_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_test_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_test_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_logic_grid_cell.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_logic_grid_cell.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_fragment_today.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_today.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_fragment_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_spatial_rotation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_spatial_rotation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_item_daily_challenge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\item_daily_challenge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\drawable_math_problem_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\drawable\\math_problem_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_layout_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\layout_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-debug-4:\\layout_activity_visual_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.leapiq.braintraining.app-main-6:\\layout\\activity_visual_search.xml"}]