<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_white"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="Memory Assessment"
        app:titleTextColor="@color/text_primary" />

    <!-- Progress Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/surface_white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/progress_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Task 1 of 20"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/task_type_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Working Memory"
                android:textColor="@color/primary_light_blue"
                android:textSize="14sp" />

        </LinearLayout>

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="8dp"
            android:progressTint="@color/primary_light_blue" />

    </LinearLayout>

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Instructions -->
            <TextView
                android:id="@+id/instruction_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="Follow the instructions for each memory task"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:gravity="center"
                android:background="@drawable/rounded_background"
                android:padding="16dp" />

            <!-- Digit Span Task -->
            <LinearLayout
                android:id="@+id/digit_span_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- Digit Display -->
                <TextView
                    android:id="@+id/digit_display"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:gravity="center"
                    android:text="7"
                    android:textColor="@color/text_primary"
                    android:textSize="72sp"
                    android:textStyle="bold"
                    android:background="@drawable/rounded_background"
                    android:layout_marginBottom="16dp" />

                <!-- Response Display -->
                <TextView
                    android:id="@+id/response_display"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text=""
                    android:textColor="@color/text_primary"
                    android:textSize="24sp"
                    android:gravity="center"
                    android:background="@drawable/rounded_background"
                    android:padding="16dp"
                    android:minHeight="60dp" />

                <!-- Number Pad -->
                <LinearLayout
                    android:id="@+id/number_pad_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <Button
                            android:id="@+id/btn_1"
                            android:layout_width="0dp"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:layout_margin="4dp"
                            android:text="1"
                            android:textSize="18sp"
                            app:backgroundTint="@color/surface_white"
                            android:textColor="@color/text_primary" />

                        <Button
                            android:id="@+id/btn_2"
                            android:layout_width="0dp"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:layout_margin="4dp"
                            android:text="2"
                            android:textSize="18sp"
                            app:backgroundTint="@color/surface_white"
                            android:textColor="@color/text_primary" />

                        <Button
                            android:id="@+id/btn_3"
                            android:layout_width="0dp"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:layout_margin="4dp"
                            android:text="3"
                            android:textSize="18sp"
                            app:backgroundTint="@color/surface_white"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <Button
                            android:id="@+id/btn_4"
                            android:layout_width="0dp"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:layout_margin="4dp"
                            android:text="4"
                            android:textSize="18sp"
                            app:backgroundTint="@color/surface_white"
                            android:textColor="@color/text_primary" />

                        <Button
                            android:id="@+id/btn_5"
                            android:layout_width="0dp"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:layout_margin="4dp"
                            android:text="5"
                            android:textSize="18sp"
                            app:backgroundTint="@color/surface_white"
                            android:textColor="@color/text_primary" />

                        <Button
                            android:id="@+id/btn_6"
                            android:layout_width="0dp"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:layout_margin="4dp"
                            android:text="6"
                            android:textSize="18sp"
                            app:backgroundTint="@color/surface_white"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <Button
                            android:id="@+id/btn_7"
                            android:layout_width="0dp"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:layout_margin="4dp"
                            android:text="7"
                            android:textSize="18sp"
                            app:backgroundTint="@color/surface_white"
                            android:textColor="@color/text_primary" />

                        <Button
                            android:id="@+id/btn_8"
                            android:layout_width="0dp"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:layout_margin="4dp"
                            android:text="8"
                            android:textSize="18sp"
                            app:backgroundTint="@color/surface_white"
                            android:textColor="@color/text_primary" />

                        <Button
                            android:id="@+id/btn_9"
                            android:layout_width="0dp"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:layout_margin="4dp"
                            android:text="9"
                            android:textSize="18sp"
                            app:backgroundTint="@color/surface_white"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <Button
                        android:id="@+id/btn_0"
                        android:layout_width="120dp"
                        android:layout_height="60dp"
                        android:layout_gravity="center"
                        android:layout_margin="4dp"
                        android:text="0"
                        android:textSize="18sp"
                        app:backgroundTint="@color/surface_white"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

            <!-- Spatial Span Task -->
            <LinearLayout
                android:id="@+id/spatial_span_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/memory_grid"
                    android:layout_width="match_parent"
                    android:layout_height="300dp"
                    android:layout_marginBottom="16dp" />

            </LinearLayout>

            <!-- Word List Task -->
            <LinearLayout
                android:id="@+id/word_list_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/word_list_display"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginBottom="16dp"
                    android:text="Apple\nBook\nChair"
                    android:textColor="@color/text_primary"
                    android:textSize="24sp"
                    android:gravity="center"
                    android:background="@drawable/rounded_background"
                    android:padding="16dp" />

                <LinearLayout
                    android:id="@+id/word_input_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <EditText
                        android:id="@+id/word_input"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:layout_marginBottom="16dp"
                        android:hint="Enter words (one per line)"
                        android:gravity="top|start"
                        android:inputType="textMultiLine"
                        android:background="@drawable/rounded_background"
                        android:padding="16dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Pattern Recall Task -->
            <LinearLayout
                android:id="@+id/pattern_recall_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- Uses the same memory_grid RecyclerView -->

            </LinearLayout>

            <!-- Delayed Recall Task -->
            <LinearLayout
                android:id="@+id/delayed_recall_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/delayed_recall_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="Recall question will appear here"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:background="@drawable/rounded_background"
                    android:padding="16dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/surface_white"
        android:elevation="4dp">

        <Button
            android:id="@+id/btn_clear_response"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Clear"
            android:textColor="@color/text_secondary"
            app:strokeColor="@color/text_secondary" />

        <Button
            android:id="@+id/btn_submit_response"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:layout_marginStart="8dp"
            android:text="Submit"
            android:textColor="@color/surface_white"
            app:backgroundTint="@color/primary_light_blue" />

    </LinearLayout>

</LinearLayout>
