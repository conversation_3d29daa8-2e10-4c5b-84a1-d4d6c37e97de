<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="top">

    <!-- Step Number Circle -->
    <androidx.cardview.widget.CardView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="2dp"
        app:cardCornerRadius="16dp"
        app:cardBackgroundColor="@color/primary_light_blue"
        app:cardElevation="0dp">

        <TextView
            android:id="@+id/instruction_number"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:text="1"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/surface_white"
            android:gravity="center" />

    </androidx.cardview.widget.CardView>

    <!-- Instruction Text -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Instruction text will appear here"
        android:textSize="15sp"
        android:textColor="@color/text_secondary"
        android:lineSpacingMultiplier="1.2"
        android:layout_marginBottom="8dp" />

</LinearLayout>
