#Wed Jul 02 01:54:21 PDT 2025
com.leapiq.braintraining.app-main-6\:/anim/fade_in.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fade_in.xml.flat
com.leapiq.braintraining.app-main-6\:/anim/fade_out.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fade_out.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/anagram_input_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_anagram_input_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/anagram_scrambled_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_anagram_scrambled_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/button_danger.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_danger.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/button_primary.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_primary.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/button_primary_rounded.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_primary_rounded.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/button_secondary.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_secondary.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/card_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_card_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/dialog_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/estimation_answer_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_estimation_answer_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/estimation_display_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_estimation_display_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/focus_stimulus_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_focus_stimulus_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/game_card_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_game_card_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/gradient_learning_style.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_learning_style.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/gradient_problem_solving.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_problem_solving.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/gradient_stress_response.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_stress_response.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_disk_blue.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_disk_blue.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_disk_cyan.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_disk_cyan.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_disk_green.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_disk_green.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_disk_orange.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_disk_orange.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_disk_pink.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_disk_pink.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_disk_purple.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_disk_purple.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_disk_red.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_disk_red.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_disk_yellow.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_disk_yellow.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_tower_base.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_tower_base.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_tower_normal_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_tower_normal_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_tower_peg.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_tower_peg.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/hanoi_tower_selected_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_hanoi_tower_selected_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/header_icon_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_header_icon_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_analytics.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_analytics.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_apple.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_apple.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_arrow_back.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_back.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_car.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_car.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_card_back.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_card_back.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_check.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_circle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_circle.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_close.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_diamond.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_diamond.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_favorite.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_favorite.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_games.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_games.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_heart.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_heart.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_home.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_lock.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_lock.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_menu.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_music.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_music.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_person.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_person.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_pets.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pets.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_progress.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_progress.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_school.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_school.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_settings.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_sports.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_sports.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_square.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_square.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_star.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_star.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_streak.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_streak.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_test_placeholder.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_test_placeholder.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_tests.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tests.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_timer.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_timer.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_today.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_today.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_triangle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_triangle.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/ic_trophy.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_trophy.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/logic_premises_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logic_premises_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/logic_question_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logic_question_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/logical_reasoning.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logical_reasoning.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/math_answer_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_math_answer_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/math_problem_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_math_problem_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/memory_grid_default.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_memory_grid_default.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/memory_grid_highlighted.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_memory_grid_highlighted.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/memory_grid_pattern.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_memory_grid_pattern.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/memory_grid_selected.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_memory_grid_selected.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/number_display_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_number_display_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/number_input_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_number_input_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/pattern_cell_empty.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_pattern_cell_empty.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/pattern_cell_filled.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_pattern_cell_filled.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/pattern_display_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_pattern_display_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/pattern_question_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_pattern_question_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/premium_button_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_premium_button_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/reaction_stimulus_circle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_reaction_stimulus_circle.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/rounded_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/search_shape_circle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_search_shape_circle.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/search_shape_diamond.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_search_shape_diamond.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/search_shape_square.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_search_shape_square.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/search_shape_triangle.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_search_shape_triangle.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/sequence_answer_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_sequence_answer_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/sequence_button_blue.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_sequence_button_blue.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/sequence_button_green.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_sequence_button_green.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/sequence_button_red.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_sequence_button_red.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/sequence_button_yellow.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_sequence_button_yellow.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/sequence_display_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_sequence_display_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/spatial_rotation.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spatial_rotation.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/spatial_shape_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spatial_shape_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/speed_math_answer_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_speed_math_answer_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/speed_math_problem_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_speed_math_problem_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/stroop_word_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_stroop_word_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tower_of_hanoi.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tower_of_hanoi.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_ball_blue.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_ball_blue.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_ball_cyan.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_ball_cyan.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_ball_green.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_ball_green.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_ball_orange.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_ball_orange.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_ball_pink.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_ball_pink.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_ball_purple.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_ball_purple.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_ball_red.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_ball_red.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_ball_yellow.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_ball_yellow.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_empty_slot.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_empty_slot.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_normal_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_normal_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/tube_selected_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tube_selected_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/vocabulary_prompt_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_vocabulary_prompt_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/word_search_cell_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_word_search_cell_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/word_search_grid_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_word_search_grid_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/word_search_list_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_word_search_list_background.xml.flat
com.leapiq.braintraining.app-main-6\:/drawable/word_target_background.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_word_target_background.xml.flat
com.leapiq.braintraining.app-main-6\:/menu/bottom_nav_menu.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_nav_menu.xml.flat
com.leapiq.braintraining.app-main-6\:/navigation/nav_graph.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
com.leapiq.braintraining.app-main-6\:/xml/backup_rules.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.leapiq.braintraining.app-main-6\:/xml/data_extraction_rules.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_all_results.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_all_results.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_anagrams.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_anagrams.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_analytics.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_analytics.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_attention_test.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_attention_test.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_card_matching.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_card_matching.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_estimation.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_estimation.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_focus_challenge.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_focus_challenge.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_game_result.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_game_result.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_learning_style.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_learning_style.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_logic_puzzles.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_logic_puzzles.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_logical_reasoning.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_logical_reasoning.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_main.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_memory_assessment.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_memory_assessment.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_mental_arithmetic.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_mental_arithmetic.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_my_results.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_my_results.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_number_memory.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_number_memory.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_number_sequences.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_number_sequences.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_pattern_completion.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_pattern_completion.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_pattern_memory.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_pattern_memory.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_problem_solving_style.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_problem_solving_style.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_processing_speed.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_processing_speed.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_reaction_time.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_reaction_time.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_sequence_recall.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_sequence_recall.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_spatial_rotation.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_spatial_rotation.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_speed_math.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_speed_math.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_stress_response.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_stress_response.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_stroop_test.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_stroop_test.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_test_info.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_test_info.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_test_progress.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_test_progress.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_test_question_base.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_test_question_base.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_test_results.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_test_results.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_tower_of_hanoi.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_tower_of_hanoi.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_tube_sort.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_tube_sort.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_visual_search.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_visual_search.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_vocabulary.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_vocabulary.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_word_association.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_word_association.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/activity_word_search.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_word_search.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/dialog_game_menu.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_game_menu.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/fragment_games.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_games.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/fragment_profile.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_profile.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/fragment_progress.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_progress.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/fragment_tests.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tests.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/fragment_today.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_today.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_achievement.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_achievement.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_all_results.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_all_results.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_category_accuracy.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_accuracy.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_daily_challenge.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_daily_challenge.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_detailed_score.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_detailed_score.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_game_card.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_game_card.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_hanoi_tower.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_hanoi_tower.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_learning_style_option.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_learning_style_option.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_logic_clue.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_logic_clue.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_logic_grid_cell.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_logic_grid_cell.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_logic_grid_header.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_logic_grid_header.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_logic_grid_row.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_logic_grid_row.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_memory_card.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_memory_card.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_memory_grid.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_memory_grid.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_pattern_cell.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_pattern_cell.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_problem_solving_option.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_problem_solving_option.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_recent_activity.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_recent_activity.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_search_shape.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_search_shape.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_stress_response_option.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_stress_response_option.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_test_card.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_test_card.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_test_instruction.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_test_instruction.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_test_progress.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_test_progress.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_test_result.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_test_result.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_tube.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_tube.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_word_search_cell.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_word_search_cell.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/item_word_search_word.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_word_search_word.xml.flat
com.leapiq.braintraining.app-mergeDebugResources-3\:/layout/layout_header.xml=C\:\\Projects\\LeapIQ\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_layout_header.xml.flat
