package com.leapiq.braintraining.ui.tests

import com.leapiq.braintraining.R

/**
 * Information page for Learning Style Test
 * Shows test description, instructions, and start button
 */
class LearningStyleInfoActivity : BaseTestInfoActivity() {
    
    override fun loadTestInfo() {
        setTestInfo(
            title = "Learning Style Assessment",
            subtitle = "Personality Test",
            description = "Discover your preferred learning style and how you best process information. This assessment will help you understand whether you learn better through visual, auditory, or kinesthetic methods.",
            instructions = listOf(
                "Read each question carefully and think about your natural preferences",
                "Choose the answer that best describes how you typically behave or feel",
                "There are no right or wrong answers - be honest about your preferences",
                "You can go back and change your answers using the Previous button",
                "The test takes about 8-10 minutes to complete"
            ),
            estimatedMinutes = 10,
            iconResource = R.drawable.ic_learning
        )
    }
    
    override fun getTestActivityClass(): Class<*> {
        return LearningStyleActivity::class.java
    }
    
    override fun getHeroGradient(): Int {
        return R.drawable.gradient_learning_style
    }
}
