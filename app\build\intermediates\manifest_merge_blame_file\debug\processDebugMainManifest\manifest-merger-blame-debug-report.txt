1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.leapiq.braintraining"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:5:5-225:19
18        android:allowBackup="true"
18-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@drawable/ic_today"
24-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:9:9-42
25        android:label="@string/app_name"
25-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@drawable/ic_today"
26-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:11:9-47
27        android:supportsRtl="true"
27-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.LeapIQ" >
29-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:13:9-44
30        <activity
30-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:16:9-24:20
31            android:name="com.leapiq.braintraining.MainActivity"
31-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:17:13-41
32            android:exported="true"
32-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:18:13-36
33            android:theme="@style/Theme.LeapIQ" >
33-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:19:13-48
34            <intent-filter>
34-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:20:13-23:29
35                <action android:name="android.intent.action.MAIN" />
35-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:21:17-69
35-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:21:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:22:17-77
37-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:22:27-74
38            </intent-filter>
39        </activity>
40
41        <!-- Game Activities -->
42        <activity
42-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:27:9-31:52
43            android:name="com.leapiq.braintraining.ui.games.CardMatchingActivity"
43-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:28:13-58
44            android:exported="false"
44-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:29:13-37
45            android:screenOrientation="portrait"
45-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:31:13-49
46            android:theme="@style/Theme.LeapIQ" />
46-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:30:13-48
47        <activity
47-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:33:9-37:52
48            android:name="com.leapiq.braintraining.ui.games.SpeedMathActivity"
48-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:34:13-55
49            android:exported="false"
49-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:35:13-37
50            android:screenOrientation="portrait"
50-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:37:13-49
51            android:theme="@style/Theme.LeapIQ" />
51-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:36:13-48
52        <activity
52-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:39:9-43:52
53            android:name="com.leapiq.braintraining.ui.games.StroopTestActivity"
53-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:40:13-56
54            android:exported="false"
54-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:41:13-37
55            android:screenOrientation="portrait"
55-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:43:13-49
56            android:theme="@style/Theme.LeapIQ" />
56-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:42:13-48
57        <activity
57-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:45:9-49:52
58            android:name="com.leapiq.braintraining.ui.games.VocabularyActivity"
58-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:46:13-56
59            android:exported="false"
59-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:47:13-37
60            android:screenOrientation="portrait"
60-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:49:13-49
61            android:theme="@style/Theme.LeapIQ" />
61-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:48:13-48
62        <activity
62-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:51:9-55:52
63            android:name="com.leapiq.braintraining.ui.games.WordSearchActivity"
63-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:52:13-56
64            android:exported="false"
64-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:53:13-37
65            android:screenOrientation="portrait"
65-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:55:13-49
66            android:theme="@style/Theme.LeapIQ" />
66-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:54:13-48
67
68        <!-- Memory Games -->
69        <activity
69-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:58:9-62:52
70            android:name="com.leapiq.braintraining.ui.games.SequenceRecallActivity"
70-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:59:13-60
71            android:exported="false"
71-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:60:13-37
72            android:screenOrientation="portrait"
72-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:62:13-49
73            android:theme="@style/Theme.LeapIQ" />
73-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:61:13-48
74        <activity
74-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:64:9-68:52
75            android:name="com.leapiq.braintraining.ui.games.PatternMemoryActivity"
75-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:65:13-59
76            android:exported="false"
76-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:66:13-37
77            android:screenOrientation="portrait"
77-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:68:13-49
78            android:theme="@style/Theme.LeapIQ" />
78-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:67:13-48
79        <activity
79-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:70:9-74:52
80            android:name="com.leapiq.braintraining.ui.games.NumberMemoryActivity"
80-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:71:13-58
81            android:exported="false"
81-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:72:13-37
82            android:screenOrientation="portrait"
82-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:74:13-49
83            android:theme="@style/Theme.LeapIQ" />
83-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:73:13-48
84
85        <!-- Attention Games -->
86        <activity
86-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:77:9-81:52
87            android:name="com.leapiq.braintraining.ui.games.ReactionTimeActivity"
87-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:78:13-58
88            android:exported="false"
88-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:79:13-37
89            android:screenOrientation="portrait"
89-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:81:13-49
90            android:theme="@style/Theme.LeapIQ" />
90-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:80:13-48
91        <activity
91-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:83:9-87:52
92            android:name="com.leapiq.braintraining.ui.games.VisualSearchActivity"
92-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:84:13-58
93            android:exported="false"
93-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:85:13-37
94            android:screenOrientation="portrait"
94-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:87:13-49
95            android:theme="@style/Theme.LeapIQ" />
95-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:86:13-48
96        <activity
96-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:89:9-93:52
97            android:name="com.leapiq.braintraining.ui.games.FocusChallengeActivity"
97-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:90:13-60
98            android:exported="false"
98-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:91:13-37
99            android:screenOrientation="portrait"
99-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:93:13-49
100            android:theme="@style/Theme.LeapIQ" />
100-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:92:13-48
101
102        <!-- Math Games -->
103        <activity
103-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:96:9-100:52
104            android:name="com.leapiq.braintraining.ui.games.MentalArithmeticActivity"
104-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:97:13-62
105            android:exported="false"
105-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:98:13-37
106            android:screenOrientation="portrait"
106-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:100:13-49
107            android:theme="@style/Theme.LeapIQ" />
107-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:99:13-48
108        <activity
108-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:102:9-106:52
109            android:name="com.leapiq.braintraining.ui.games.NumberSequencesActivity"
109-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:103:13-61
110            android:exported="false"
110-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:104:13-37
111            android:screenOrientation="portrait"
111-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:106:13-49
112            android:theme="@style/Theme.LeapIQ" />
112-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:105:13-48
113        <activity
113-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:108:9-112:52
114            android:name="com.leapiq.braintraining.ui.games.EstimationActivity"
114-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:109:13-56
115            android:exported="false"
115-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:110:13-37
116            android:screenOrientation="portrait"
116-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:112:13-49
117            android:theme="@style/Theme.LeapIQ" />
117-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:111:13-48
118
119        <!-- Logic Games -->
120        <activity
120-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:115:9-119:52
121            android:name="com.leapiq.braintraining.ui.games.TubeSortActivity"
121-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:116:13-54
122            android:exported="false"
122-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:117:13-37
123            android:screenOrientation="portrait"
123-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:119:13-49
124            android:theme="@style/Theme.LeapIQ" />
124-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:118:13-48
125        <activity
125-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:121:9-125:52
126            android:name="com.leapiq.braintraining.ui.games.LogicalReasoningActivity"
126-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:122:13-62
127            android:exported="false"
127-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:123:13-37
128            android:screenOrientation="portrait"
128-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:125:13-49
129            android:theme="@style/Theme.LeapIQ" />
129-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:124:13-48
130        <activity
130-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:127:9-131:52
131            android:name="com.leapiq.braintraining.ui.games.SpatialRotationActivity"
131-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:128:13-61
132            android:exported="false"
132-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:129:13-37
133            android:screenOrientation="portrait"
133-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:131:13-49
134            android:theme="@style/Theme.LeapIQ" />
134-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:130:13-48
135        <activity
135-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:133:9-137:52
136            android:name="com.leapiq.braintraining.ui.games.TowerOfHanoiActivity"
136-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:134:13-58
137            android:exported="false"
137-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:135:13-37
138            android:screenOrientation="portrait"
138-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:137:13-49
139            android:theme="@style/Theme.LeapIQ" />
139-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:136:13-48
140        <activity
140-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:139:9-143:52
141            android:name="com.leapiq.braintraining.ui.games.PatternCompletionActivity"
141-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:140:13-63
142            android:exported="false"
142-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:141:13-37
143            android:screenOrientation="portrait"
143-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:143:13-49
144            android:theme="@style/Theme.LeapIQ" />
144-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:142:13-48
145
146        <!-- Language Games -->
147        <activity
147-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:146:9-150:52
148            android:name="com.leapiq.braintraining.ui.games.WordAssociationActivity"
148-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:147:13-61
149            android:exported="false"
149-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:148:13-37
150            android:screenOrientation="portrait"
150-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:150:13-49
151            android:theme="@style/Theme.LeapIQ" />
151-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:149:13-48
152        <activity
152-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:152:9-156:52
153            android:name="com.leapiq.braintraining.ui.games.AnagramsActivity"
153-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:153:13-54
154            android:exported="false"
154-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:154:13-37
155            android:screenOrientation="portrait"
155-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:156:13-49
156            android:theme="@style/Theme.LeapIQ" />
156-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:155:13-48
157        <activity
157-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:158:9-162:52
158            android:name="com.leapiq.braintraining.ui.games.LogicPuzzlesActivity"
158-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:159:13-58
159            android:exported="false"
159-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:160:13-37
160            android:screenOrientation="portrait"
160-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:162:13-49
161            android:theme="@style/Theme.LeapIQ" />
161-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:161:13-48
162        <activity
162-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:164:9-168:52
163            android:name="com.leapiq.braintraining.ui.games.GameResultActivity"
163-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:165:13-56
164            android:exported="false"
164-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:166:13-37
165            android:screenOrientation="portrait"
165-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:168:13-49
166            android:theme="@style/Theme.LeapIQ" />
166-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:167:13-48
167
168        <!-- Test Activities -->
169        <activity
169-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:171:9-175:52
170            android:name="com.leapiq.braintraining.ui.tests.MemoryAssessmentActivity"
170-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:172:13-62
171            android:exported="false"
171-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:173:13-37
172            android:screenOrientation="portrait"
172-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:175:13-49
173            android:theme="@style/Theme.LeapIQ" />
173-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:174:13-48
174        <activity
174-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:177:9-181:52
175            android:name="com.leapiq.braintraining.ui.tests.AttentionTestActivity"
175-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:178:13-59
176            android:exported="false"
176-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:179:13-37
177            android:screenOrientation="portrait"
177-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:181:13-49
178            android:theme="@style/Theme.LeapIQ" />
178-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:180:13-48
179        <activity
179-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:183:9-187:52
180            android:name="com.leapiq.braintraining.ui.tests.ProcessingSpeedActivity"
180-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:184:13-61
181            android:exported="false"
181-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:185:13-37
182            android:screenOrientation="portrait"
182-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:187:13-49
183            android:theme="@style/Theme.LeapIQ" />
183-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:186:13-48
184        <activity
184-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:189:9-193:52
185            android:name="com.leapiq.braintraining.ui.tests.LearningStyleActivity"
185-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:190:13-59
186            android:exported="false"
186-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:191:13-37
187            android:screenOrientation="portrait"
187-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:193:13-49
188            android:theme="@style/Theme.LeapIQ" />
188-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:192:13-48
189        <activity
189-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:195:9-199:52
190            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity"
190-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:196:13-65
191            android:exported="false"
191-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:197:13-37
192            android:screenOrientation="portrait"
192-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:199:13-49
193            android:theme="@style/Theme.LeapIQ" />
193-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:198:13-48
194        <activity
194-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:201:9-205:52
195            android:name="com.leapiq.braintraining.ui.tests.StressResponseActivity"
195-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:202:13-60
196            android:exported="false"
196-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:203:13-37
197            android:screenOrientation="portrait"
197-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:205:13-49
198            android:theme="@style/Theme.LeapIQ" />
198-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:204:13-48
199        <activity
199-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:207:9-211:52
200            android:name="com.leapiq.braintraining.ui.tests.results.TestResultsActivity"
200-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:208:13-65
201            android:exported="false"
201-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:209:13-37
202            android:screenOrientation="portrait"
202-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:211:13-49
203            android:theme="@style/Theme.LeapIQ" />
203-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:210:13-48
204        <activity
204-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:213:9-217:52
205            android:name="com.leapiq.braintraining.ui.tests.results.AllResultsActivity"
205-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:214:13-64
206            android:exported="false"
206-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:215:13-37
207            android:screenOrientation="portrait"
207-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:217:13-49
208            android:theme="@style/Theme.LeapIQ" />
208-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:216:13-48
209        <activity
209-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:219:9-223:52
210            android:name="com.leapiq.braintraining.ui.tests.results.MyResultsActivity"
210-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:220:13-63
211            android:exported="false"
211-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:221:13-37
212            android:screenOrientation="portrait"
212-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:223:13-49
213            android:theme="@style/Theme.LeapIQ" />
213-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:222:13-48
214
215        <provider
215-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
216            android:name="androidx.startup.InitializationProvider"
216-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
217            android:authorities="com.leapiq.braintraining.androidx-startup"
217-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
218            android:exported="false" >
218-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
219            <meta-data
219-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
220                android:name="androidx.emoji2.text.EmojiCompatInitializer"
220-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
221                android:value="androidx.startup" />
221-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
222            <meta-data
222-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
223                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
223-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
224                android:value="androidx.startup" />
224-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
225            <meta-data
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
226                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
227                android:value="androidx.startup" />
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
228        </provider>
229
230        <uses-library
230-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
231            android:name="androidx.window.extensions"
231-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
232            android:required="false" />
232-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
233        <uses-library
233-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
234            android:name="androidx.window.sidecar"
234-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
235            android:required="false" />
235-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
236
237        <receiver
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
238            android:name="androidx.profileinstaller.ProfileInstallReceiver"
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
239            android:directBootAware="false"
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
240            android:enabled="true"
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
241            android:exported="true"
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
242            android:permission="android.permission.DUMP" >
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
244                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
247                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
248            </intent-filter>
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
250                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
251            </intent-filter>
252            <intent-filter>
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
253                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
254            </intent-filter>
255        </receiver>
256    </application>
257
258</manifest>
