1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.leapiq.braintraining"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:5:5-235:19
18        android:allowBackup="true"
18-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.LeapIQ" >
29-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:13:9-44
30
31        <!-- Splash Screen Activity (Main Launcher) -->
32        <activity
32-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:17:9-26:20
33            android:name="com.leapiq.braintraining.SplashActivity"
33-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:18:13-43
34            android:exported="true"
34-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:19:13-36
35            android:screenOrientation="portrait"
35-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:21:13-49
36            android:theme="@style/Theme.LeapIQ.Splash" >
36-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:20:13-55
37            <intent-filter>
37-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:22:13-25:29
38                <action android:name="android.intent.action.MAIN" />
38-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:23:17-69
38-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:23:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:24:17-77
40-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:24:27-74
41            </intent-filter>
42        </activity>
43
44        <!-- Main Activity -->
45        <activity
45-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:29:9-33:52
46            android:name="com.leapiq.braintraining.MainActivity"
46-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:30:13-41
47            android:exported="false"
47-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:31:13-37
48            android:screenOrientation="portrait"
48-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:33:13-49
49            android:theme="@style/Theme.LeapIQ" />
49-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:32:13-48
50
51        <!-- Game Activities -->
52        <activity
52-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:36:9-40:52
53            android:name="com.leapiq.braintraining.ui.games.CardMatchingActivity"
53-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:37:13-58
54            android:exported="false"
54-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:38:13-37
55            android:screenOrientation="portrait"
55-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:40:13-49
56            android:theme="@style/Theme.LeapIQ" />
56-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:39:13-48
57        <activity
57-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:42:9-46:52
58            android:name="com.leapiq.braintraining.ui.games.SpeedMathActivity"
58-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:43:13-55
59            android:exported="false"
59-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:44:13-37
60            android:screenOrientation="portrait"
60-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:46:13-49
61            android:theme="@style/Theme.LeapIQ" />
61-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:45:13-48
62        <activity
62-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:48:9-52:52
63            android:name="com.leapiq.braintraining.ui.games.StroopTestActivity"
63-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:49:13-56
64            android:exported="false"
64-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:50:13-37
65            android:screenOrientation="portrait"
65-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:52:13-49
66            android:theme="@style/Theme.LeapIQ" />
66-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:51:13-48
67        <activity
67-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:54:9-58:52
68            android:name="com.leapiq.braintraining.ui.games.VocabularyActivity"
68-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:55:13-56
69            android:exported="false"
69-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:56:13-37
70            android:screenOrientation="portrait"
70-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:58:13-49
71            android:theme="@style/Theme.LeapIQ" />
71-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:57:13-48
72        <activity
72-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:60:9-64:52
73            android:name="com.leapiq.braintraining.ui.games.WordSearchActivity"
73-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:61:13-56
74            android:exported="false"
74-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:62:13-37
75            android:screenOrientation="portrait"
75-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:64:13-49
76            android:theme="@style/Theme.LeapIQ" />
76-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:63:13-48
77
78        <!-- Memory Games -->
79        <activity
79-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:67:9-71:52
80            android:name="com.leapiq.braintraining.ui.games.SequenceRecallActivity"
80-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:68:13-60
81            android:exported="false"
81-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:69:13-37
82            android:screenOrientation="portrait"
82-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:71:13-49
83            android:theme="@style/Theme.LeapIQ" />
83-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:70:13-48
84        <activity
84-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:73:9-77:52
85            android:name="com.leapiq.braintraining.ui.games.PatternMemoryActivity"
85-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:74:13-59
86            android:exported="false"
86-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:75:13-37
87            android:screenOrientation="portrait"
87-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:77:13-49
88            android:theme="@style/Theme.LeapIQ" />
88-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:76:13-48
89        <activity
89-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:79:9-83:52
90            android:name="com.leapiq.braintraining.ui.games.NumberMemoryActivity"
90-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:80:13-58
91            android:exported="false"
91-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:81:13-37
92            android:screenOrientation="portrait"
92-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:83:13-49
93            android:theme="@style/Theme.LeapIQ" />
93-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:82:13-48
94
95        <!-- Attention Games -->
96        <activity
96-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:86:9-90:52
97            android:name="com.leapiq.braintraining.ui.games.ReactionTimeActivity"
97-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:87:13-58
98            android:exported="false"
98-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:88:13-37
99            android:screenOrientation="portrait"
99-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:90:13-49
100            android:theme="@style/Theme.LeapIQ" />
100-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:89:13-48
101        <activity
101-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:92:9-96:52
102            android:name="com.leapiq.braintraining.ui.games.VisualSearchActivity"
102-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:93:13-58
103            android:exported="false"
103-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:94:13-37
104            android:screenOrientation="portrait"
104-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:96:13-49
105            android:theme="@style/Theme.LeapIQ" />
105-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:95:13-48
106        <activity
106-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:98:9-102:52
107            android:name="com.leapiq.braintraining.ui.games.FocusChallengeActivity"
107-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:99:13-60
108            android:exported="false"
108-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:100:13-37
109            android:screenOrientation="portrait"
109-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:102:13-49
110            android:theme="@style/Theme.LeapIQ" />
110-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:101:13-48
111
112        <!-- Math Games -->
113        <activity
113-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:105:9-109:52
114            android:name="com.leapiq.braintraining.ui.games.MentalArithmeticActivity"
114-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:106:13-62
115            android:exported="false"
115-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:107:13-37
116            android:screenOrientation="portrait"
116-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:109:13-49
117            android:theme="@style/Theme.LeapIQ" />
117-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:108:13-48
118        <activity
118-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:111:9-115:52
119            android:name="com.leapiq.braintraining.ui.games.NumberSequencesActivity"
119-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:112:13-61
120            android:exported="false"
120-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:113:13-37
121            android:screenOrientation="portrait"
121-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:115:13-49
122            android:theme="@style/Theme.LeapIQ" />
122-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:114:13-48
123        <activity
123-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:117:9-121:52
124            android:name="com.leapiq.braintraining.ui.games.EstimationActivity"
124-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:118:13-56
125            android:exported="false"
125-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:119:13-37
126            android:screenOrientation="portrait"
126-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:121:13-49
127            android:theme="@style/Theme.LeapIQ" />
127-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:120:13-48
128
129        <!-- Logic Games -->
130        <activity
130-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:124:9-128:52
131            android:name="com.leapiq.braintraining.ui.games.TubeSortActivity"
131-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:125:13-54
132            android:exported="false"
132-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:126:13-37
133            android:screenOrientation="portrait"
133-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:128:13-49
134            android:theme="@style/Theme.LeapIQ" />
134-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:127:13-48
135        <activity
135-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:130:9-134:52
136            android:name="com.leapiq.braintraining.ui.games.LogicalReasoningActivity"
136-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:131:13-62
137            android:exported="false"
137-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:132:13-37
138            android:screenOrientation="portrait"
138-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:134:13-49
139            android:theme="@style/Theme.LeapIQ" />
139-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:133:13-48
140        <activity
140-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:136:9-140:52
141            android:name="com.leapiq.braintraining.ui.games.SpatialRotationActivity"
141-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:137:13-61
142            android:exported="false"
142-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:138:13-37
143            android:screenOrientation="portrait"
143-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:140:13-49
144            android:theme="@style/Theme.LeapIQ" />
144-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:139:13-48
145        <activity
145-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:142:9-146:52
146            android:name="com.leapiq.braintraining.ui.games.TowerOfHanoiActivity"
146-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:143:13-58
147            android:exported="false"
147-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:144:13-37
148            android:screenOrientation="portrait"
148-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:146:13-49
149            android:theme="@style/Theme.LeapIQ" />
149-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:145:13-48
150        <activity
150-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:148:9-152:52
151            android:name="com.leapiq.braintraining.ui.games.PatternCompletionActivity"
151-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:149:13-63
152            android:exported="false"
152-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:150:13-37
153            android:screenOrientation="portrait"
153-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:152:13-49
154            android:theme="@style/Theme.LeapIQ" />
154-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:151:13-48
155
156        <!-- Language Games -->
157        <activity
157-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:155:9-159:52
158            android:name="com.leapiq.braintraining.ui.games.WordAssociationActivity"
158-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:156:13-61
159            android:exported="false"
159-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:157:13-37
160            android:screenOrientation="portrait"
160-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:159:13-49
161            android:theme="@style/Theme.LeapIQ" />
161-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:158:13-48
162        <activity
162-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:161:9-165:52
163            android:name="com.leapiq.braintraining.ui.games.AnagramsActivity"
163-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:162:13-54
164            android:exported="false"
164-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:163:13-37
165            android:screenOrientation="portrait"
165-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:165:13-49
166            android:theme="@style/Theme.LeapIQ" />
166-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:164:13-48
167        <activity
167-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:167:9-171:52
168            android:name="com.leapiq.braintraining.ui.games.LogicPuzzlesActivity"
168-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:168:13-58
169            android:exported="false"
169-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:169:13-37
170            android:screenOrientation="portrait"
170-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:171:13-49
171            android:theme="@style/Theme.LeapIQ" />
171-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:170:13-48
172        <activity
172-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:173:9-177:52
173            android:name="com.leapiq.braintraining.ui.games.GameResultActivity"
173-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:174:13-56
174            android:exported="false"
174-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:175:13-37
175            android:screenOrientation="portrait"
175-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:177:13-49
176            android:theme="@style/Theme.LeapIQ" />
176-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:176:13-48
177
178        <!-- Test Info Activities -->
179        <activity
179-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:180:9-184:52
180            android:name="com.leapiq.braintraining.ui.tests.LearningStyleInfoActivity"
180-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:181:13-63
181            android:exported="false"
181-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:182:13-37
182            android:screenOrientation="portrait"
182-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:184:13-49
183            android:theme="@style/Theme.LeapIQ" />
183-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:183:13-48
184        <activity
184-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:186:9-190:52
185            android:name="com.leapiq.braintraining.ui.tests.StressResponseInfoActivity"
185-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:187:13-64
186            android:exported="false"
186-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:188:13-37
187            android:screenOrientation="portrait"
187-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:190:13-49
188            android:theme="@style/Theme.LeapIQ" />
188-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:189:13-48
189        <activity
189-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:192:9-196:52
190            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleInfoActivity"
190-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:193:13-69
191            android:exported="false"
191-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:194:13-37
192            android:screenOrientation="portrait"
192-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:196:13-49
193            android:theme="@style/Theme.LeapIQ" />
193-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:195:13-48
194
195        <!-- Test Activities -->
196        <activity
196-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:199:9-203:52
197            android:name="com.leapiq.braintraining.ui.tests.LearningStyleActivity"
197-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:200:13-59
198            android:exported="false"
198-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:201:13-37
199            android:screenOrientation="portrait"
199-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:203:13-49
200            android:theme="@style/Theme.LeapIQ" />
200-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:202:13-48
201        <activity
201-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:205:9-209:52
202            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity"
202-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:206:13-65
203            android:exported="false"
203-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:207:13-37
204            android:screenOrientation="portrait"
204-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:209:13-49
205            android:theme="@style/Theme.LeapIQ" />
205-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:208:13-48
206        <activity
206-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:211:9-215:52
207            android:name="com.leapiq.braintraining.ui.tests.StressResponseActivity"
207-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:212:13-60
208            android:exported="false"
208-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:213:13-37
209            android:screenOrientation="portrait"
209-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:215:13-49
210            android:theme="@style/Theme.LeapIQ" />
210-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:214:13-48
211        <activity
211-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:217:9-221:52
212            android:name="com.leapiq.braintraining.ui.tests.results.TestResultsActivity"
212-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:218:13-65
213            android:exported="false"
213-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:219:13-37
214            android:screenOrientation="portrait"
214-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:221:13-49
215            android:theme="@style/Theme.LeapIQ" />
215-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:220:13-48
216        <activity
216-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:223:9-227:52
217            android:name="com.leapiq.braintraining.ui.tests.results.AllResultsActivity"
217-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:224:13-64
218            android:exported="false"
218-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:225:13-37
219            android:screenOrientation="portrait"
219-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:227:13-49
220            android:theme="@style/Theme.LeapIQ" />
220-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:226:13-48
221        <activity
221-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:229:9-233:52
222            android:name="com.leapiq.braintraining.ui.tests.results.MyResultsActivity"
222-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:230:13-63
223            android:exported="false"
223-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:231:13-37
224            android:screenOrientation="portrait"
224-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:233:13-49
225            android:theme="@style/Theme.LeapIQ" />
225-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:232:13-48
226
227        <provider
227-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
228            android:name="androidx.startup.InitializationProvider"
228-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
229            android:authorities="com.leapiq.braintraining.androidx-startup"
229-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
230            android:exported="false" >
230-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
231            <meta-data
231-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
232                android:name="androidx.emoji2.text.EmojiCompatInitializer"
232-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
233                android:value="androidx.startup" />
233-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
234            <meta-data
234-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
235                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
235-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
236                android:value="androidx.startup" />
236-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
237            <meta-data
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
238                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
239                android:value="androidx.startup" />
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
240        </provider>
241
242        <uses-library
242-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
243            android:name="androidx.window.extensions"
243-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
244            android:required="false" />
244-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
245        <uses-library
245-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
246            android:name="androidx.window.sidecar"
246-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
247            android:required="false" />
247-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
248
249        <receiver
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
250            android:name="androidx.profileinstaller.ProfileInstallReceiver"
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
251            android:directBootAware="false"
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
252            android:enabled="true"
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
253            android:exported="true"
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
254            android:permission="android.permission.DUMP" >
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
255            <intent-filter>
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
256                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
257            </intent-filter>
258            <intent-filter>
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
259                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
260            </intent-filter>
261            <intent-filter>
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
262                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
263            </intent-filter>
264            <intent-filter>
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
265                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
266            </intent-filter>
267        </receiver>
268    </application>
269
270</manifest>
