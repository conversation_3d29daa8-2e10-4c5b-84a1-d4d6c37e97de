<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_test_info" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_test_info.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_test_info_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="202" endOffset="12"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="22" endOffset="54"/></Target><Target id="@+id/hero_section" view="LinearLayout"><Expressions/><location startLine="25" startOffset="8" endLine="61" endOffset="22"/></Target><Target id="@+id/test_icon" view="ImageView"><Expressions/><location startLine="33" startOffset="12" endLine="39" endOffset="53"/></Target><Target id="@+id/test_title" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="49" endOffset="42"/></Target><Target id="@+id/test_subtitle" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="59" endOffset="37"/></Target><Target id="@+id/test_description" view="TextView"><Expressions/><location startLine="94" startOffset="20" endLine="101" endOffset="51"/></Target><Target id="@+id/instructions_list" view="LinearLayout"><Expressions/><location startLine="131" startOffset="20" endLine="135" endOffset="56"/></Target><Target id="@+id/estimated_time" view="TextView"><Expressions/><location startLine="172" startOffset="20" endLine="179" endOffset="71"/></Target><Target id="@+id/btn_start_test" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="186" startOffset="12" endLine="196" endOffset="52"/></Target></Targets></Layout>