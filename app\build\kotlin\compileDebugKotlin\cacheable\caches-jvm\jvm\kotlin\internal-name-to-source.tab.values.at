T_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\AnagramsActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\CardMatchingActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\CardMatchingActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\CardMatchingActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\EstimationActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\EstimationActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\EstimationActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\FocusChallengeActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\FocusChallengeActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\GameResultActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\GameResultActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\GamesFragment.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\GamesFragment.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicPuzzlesActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicPuzzlesActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicPuzzlesActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicPuzzlesActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicalReasoningActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicalReasoningActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicalReasoningActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\MentalArithmeticActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\MentalArithmeticActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\MentalArithmeticActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\NumberMemoryActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\NumberSequencesActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\NumberSequencesActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\NumberSequencesActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\PatternCompletionActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\PatternCompletionActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\PatternCompletionActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\PatternCompletionActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\PatternMemoryActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\PatternMemoryActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\ReactionTimeActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SequenceRecallActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpatialRotationActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpatialRotationActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpatialRotationActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpatialRotationActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpeedMathActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpeedMathActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpeedMathActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\SpeedMathActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\StroopTestActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\TowerOfHanoiActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\TowerOfHanoiActivity.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\TubeSortActivity.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\TubeSortActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VisualSearchActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VisualSearchActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VisualSearchActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VisualSearchActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\WordAssociationActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\WordSearchActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\WordSearchActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\CluesAdapter.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\CluesAdapter.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\CluesAdapter.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\HanoiTowerAdapter.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\HanoiTowerAdapter.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\HanoiTowerAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\LogicGridAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\LogicGridAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\LogicGridAdapter.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\PatternGridAdapter.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\PatternGridAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\TubeAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\TubeAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\TubeAdapter.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\VisualSearchAdapter.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\VisualSearchAdapter.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordListAdapter.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordListAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordSearchGridAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordSearchGridAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCardAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCardAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCardAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCardAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\ProgressFragment.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\BaseTestActivity.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\BaseTestActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\BaseTestInfoActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\BaseTestInfoActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleInfoActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kte d$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleInfoActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseInfoActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\TestsFragment.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\TestsFragment.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\MemoryGridAdapter.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\MemoryGridAdapter.ktom\leapiq\braintraining\ui\tests\TestsFragment.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\TestsFragment.ktk j$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\LearningStyleQuestionAdapter.ktk j$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\LearningStyleQuestionAdapter.ktk j$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\LearningStyleQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\ProblemSolvingQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\ProblemSolvingQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\ProblemSolvingQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\StressResponseQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\StressResponseQuestionAdapter.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AllResultsActivity.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AllResultsActivity.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AllResultsActivity.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AllResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\TodayFragment.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\TodayFragment.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProcessingSpeedActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProcessingSpeedActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProcessingSpeedActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProcessingSpeedActivity.ktn\java\com\leapiq\braintraining\ui\games\VisualSearchActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VisualSearchActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VisualSearchActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VisualSearchActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\WordAssociationActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\WordSearchActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\WordSearchActivity.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\GameAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\GameAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\GameAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\GameAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\GameAdapter.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\HanoiTowerAdapter.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\HanoiTowerAdapter.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\HanoiTowerAdapter.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\PatternGridAdapter.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\PatternGridAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\TubeAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\TubeAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\TubeAdapter.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\VisualSearchAdapter.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\VisualSearchAdapter.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordListAdapter.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordListAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordSearchGridAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\adapter\WordSearchGridAdapter.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCard.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCardAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCardAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCardAdapter.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\memory\MemoryCardAdapter.ktP O$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\Ball.ktP O$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\Ball.ktU T$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\HanoiDisk.ktU T$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\HanoiDisk.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\HanoiTower.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\SearchItem.ktP O$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\Tube.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\WordSearchGrid.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\model\WordSearchWord.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\ProgressFragment.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\AchievementAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\AchievementAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\AchievementAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\AchievementAdapter.kth g$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\CategoryAccuracyAdapter.kth g$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\CategoryAccuracyAdapter.kth g$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\CategoryAccuracyAdapter.kth g$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\progress\adapter\CategoryAccuracyAdapter.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\AttentionTestActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\AttentionTestActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\AttentionTestActivity.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\BaseTestActivity.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\BaseTestActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt[ Z$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\LearningStyleActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\MemoryAssessmentActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\MemoryAssessmentActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\MemoryAssessmentActivity.kt^ ]$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\MemoryAssessmentActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProblemSolvingStyleActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProcessingSpeedActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProcessingSpeedActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProcessingSpeedActivity.kt] \$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\ProcessingSpeedActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\TestsFragment.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\TestsFragment.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapter\TestAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapter\TestAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapter\TestAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapter\TestAdapter.ktk j$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\LearningStyleQuestionAdapter.ktk j$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\LearningStyleQuestionAdapter.ktk j$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\LearningStyleQuestionAdapter.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\MemoryGridAdapter.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\MemoryGridAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\ProblemSolvingQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\ProblemSolvingQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\ProblemSolvingQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\StressResponseQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\StressResponseQuestionAdapter.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AllResultsActivity.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AllResultsActivity.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AllResultsActivity.kt` _$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AllResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\AnalyticsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\MyResultsActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.ktb a$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestProgressActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestResultsActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestResultsActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestResultsActivity.kta `$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\TestResultsActivity.kth g$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\AllResultsAdapter.kth g$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\AllResultsAdapter.kth g$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\AllResultsAdapter.kth g$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\AllResultsAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\RecentActivityAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\RecentActivityAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\RecentActivityAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\RecentActivityAdapter.ktj i$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\TestProgressAdapter.ktj i$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\TestProgressAdapter.ktj i$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\TestProgressAdapter.ktj i$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\TestProgressAdapter.kti h$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\TestResultsAdapter.kti h$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\TestResultsAdapter.kti h$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\TestResultsAdapter.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\TodayFragment.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\TodayFragment.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\adapter\DailyChallengeAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\adapter\DailyChallengeAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\adapter\DailyChallengeAdapter.ktc b$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\today\adapter\DailyChallengeAdapter.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\BaseGameActivity.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\BaseGameActivity.ktV U$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\BaseGameActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\StroopTestActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\StroopTestActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\TestResultsRepository.ktL K$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\Test.ktL K$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\Test.ktL K$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\Test.ktL K$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\Test.ktL K$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\Test.ktL K$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\data\model\Test.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.ktX W$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\VocabularyActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\profile\ProfileFragment.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.kt\ [$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\StressResponseActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\TestsFragment.ktS R$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\TestsFragment.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\StressResponseQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapters\StressResponseQuestionAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\DetailedScoresAdapter.ktl k$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\results\adapters\DetailedScoresAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapter\TestAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapter\TestAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapter\TestAdapter.ktY X$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\tests\adapter\TestAdapter.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicPuzzlesActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicPuzzlesActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicPuzzlesActivity.ktZ Y$PROJECT_DIR$\app\src\main\java\com\leapiq\braintraining\ui\games\LogicPuzzlesActivity.kt