<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light_gray">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/surface_white"
            android:elevation="4dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="Test Information"
            app:titleTextColor="@color/text_primary" />

        <!-- Hero Section -->
        <LinearLayout
            android:id="@+id/hero_section"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="24dp">

            <ImageView
                android:id="@+id/test_icon"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginBottom="16dp"
                android:src="@drawable/ic_test_placeholder"
                android:tint="@color/surface_white" />

            <TextView
                android:id="@+id/test_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Test Title"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@color/surface_white"
                android:gravity="center" />

            <TextView
                android:id="@+id/test_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Test Category"
                android:textSize="16sp"
                android:textColor="@color/surface_white"
                android:alpha="0.9" />

        </LinearLayout>

        <!-- Content Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Test Description Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/surface_white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="About This Test"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/test_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Test description will appear here..."
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary"
                        android:lineSpacingMultiplier="1.2" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Instructions Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/surface_white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Instructions"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:id="@+id/instructions_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Test Details Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/surface_white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_timer"
                        android:tint="@color/primary_light_blue"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Estimated Time"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/estimated_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="10 minutes"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_light_blue" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Start Test Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_start_test"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Start Test"
                android:textSize="18sp"
                android:textStyle="bold"
                app:cornerRadius="28dp"
                app:backgroundTint="@color/primary_light_blue"
                android:textColor="@color/surface_white"
                android:layout_marginBottom="24dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
