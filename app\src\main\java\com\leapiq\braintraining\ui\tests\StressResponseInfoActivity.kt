package com.leapiq.braintraining.ui.tests

import com.leapiq.braintraining.R

/**
 * Information page for Stress Response Test
 * Shows test description, instructions, and start button
 */
class StressResponseInfoActivity : BaseTestInfoActivity() {
    
    override fun loadTestInfo() {
        setTestInfo(
            title = "Stress Response Assessment",
            subtitle = "Personality Test",
            description = "Understand how you naturally respond to stress and pressure situations. This assessment will reveal your stress management patterns and coping strategies.",
            instructions = listOf(
                "Read each scenario carefully and imagine yourself in that situation",
                "Select the response that best describes how you would typically handle the situation",
                "Think about how you typically react in stressful situations",
                "Consider your natural, instinctive reactions rather than ideal responses",
                "Some scenarios test resilience, others test coping strategies and pressure handling",
                "Use Previous/Next buttons to navigate and review your answers",
                "You can change your answers before finishing the test",
                "Be honest about your typical responses for accurate results"
            ),
            estimatedMinutes = 8,
            iconResource = R.drawable.ic_stress
        )
    }
    
    override fun getTestActivityClass(): Class<*> {
        return StressResponseActivity::class.java
    }
    
    override fun getHeroGradient(): Int {
        return R.drawable.gradient_stress_response
    }
}
