package com.leapiq.braintraining.ui.tests

import com.leapiq.braintraining.R

/**
 * Information page for Stress Response Test
 * Shows test description, instructions, and start button
 */
class StressResponseInfoActivity : BaseTestInfoActivity() {
    
    override fun loadTestInfo() {
        setTestInfo(
            title = "Stress Response Assessment",
            subtitle = "Personality Test",
            description = "Understand how you naturally respond to stress and pressure situations. This assessment will reveal your stress management patterns and coping strategies.",
            instructions = listOf(
                "Think about how you typically react in stressful situations",
                "Select the response that most accurately describes your behavior",
                "Consider your natural, instinctive reactions rather than ideal responses",
                "Use Previous/Next buttons to navigate and review your answers",
                "Complete all questions for accurate results"
            ),
            estimatedMinutes = 8,
            iconResource = R.drawable.ic_stress
        )
    }
    
    override fun getTestActivityClass(): Class<*> {
        return StressResponseActivity::class.java
    }
    
    override fun getHeroGradient(): Int {
        return R.drawable.gradient_stress_response
    }
}
