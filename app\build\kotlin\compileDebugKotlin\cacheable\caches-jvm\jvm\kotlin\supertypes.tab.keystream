%com.leapiq.braintraining.MainActivity5com.leapiq.braintraining.analysis.PerformanceCategory5com.leapiq.braintraining.analysis.TimeEfficiencyLevel2com.leapiq.braintraining.analysis.ConsistencyLevel0com.leapiq.braintraining.analysis.StrengthImpact2com.leapiq.braintraining.analysis.WeaknessPriority6com.leapiq.braintraining.analysis.ImprovementPotential5com.leapiq.braintraining.analysis.ResponsePatternType=com.leapiq.braintraining.analysis.ResponsePatternSignificance0com.leapiq.braintraining.analysis.OverallBalance8com.leapiq.braintraining.analysis.RecommendationCategory8com.leapiq.braintraining.analysis.RecommendationPriority3com.leapiq.braintraining.analysis.ImprovementImpact0com.leapiq.braintraining.analysis.TrendDirection2com.leapiq.braintraining.analysis.NextStepCategory2com.leapiq.braintraining.analysis.NextStepPriority2com.leapiq.braintraining.data.BackupResult.Success0com.leapiq.braintraining.data.BackupResult.Error3com.leapiq.braintraining.data.RestoreResult.Success1com.leapiq.braintraining.data.RestoreResult.Error2com.leapiq.braintraining.data.ExportResult.Success0com.leapiq.braintraining.data.ExportResult.Error,com.leapiq.braintraining.data.TrendDirection*com.leapiq.braintraining.data.ActivityType5com.leapiq.braintraining.data.CorrelationSignificance)com.leapiq.braintraining.data.InsightType-com.leapiq.braintraining.data.InsightPriority3com.leapiq.braintraining.data.model.AchievementType0com.leapiq.braintraining.data.model.GameCategory,com.leapiq.braintraining.data.model.GameType0com.leapiq.braintraining.data.model.TestCategory,com.leapiq.braintraining.data.model.TestType2com.leapiq.braintraining.ui.games.AnagramsActivity2com.leapiq.braintraining.ui.games.BaseGameActivity6com.leapiq.braintraining.ui.games.CardMatchingActivity4com.leapiq.braintraining.ui.games.EstimationActivityCcom.leapiq.braintraining.ui.games.EstimationActivity.EstimationType8com.leapiq.braintraining.ui.games.FocusChallengeActivity4com.leapiq.braintraining.ui.games.GameResultActivity/com.leapiq.braintraining.ui.games.GamesFragment:com.leapiq.braintraining.ui.games.LogicalReasoningActivityEcom.leapiq.braintraining.ui.games.LogicalReasoningActivity.PuzzleType:com.leapiq.braintraining.ui.games.MentalArithmeticActivityDcom.leapiq.braintraining.ui.games.MentalArithmeticActivity.Operation6com.leapiq.braintraining.ui.games.NumberMemoryActivity9com.leapiq.braintraining.ui.games.NumberSequencesActivityEcom.leapiq.braintraining.ui.games.NumberSequencesActivity.PatternType;com.leapiq.braintraining.ui.games.PatternCompletionActivityGcom.leapiq.braintraining.ui.games.PatternCompletionActivity.PatternType7com.leapiq.braintraining.ui.games.PatternMemoryActivity6com.leapiq.braintraining.ui.games.ReactionTimeActivity8com.leapiq.braintraining.ui.games.SequenceRecallActivity9com.leapiq.braintraining.ui.games.SpatialRotationActivity?com.leapiq.braintraining.ui.games.SpatialRotationActivity.Shape3com.leapiq.braintraining.ui.games.SpeedMathActivity=com.leapiq.braintraining.ui.games.SpeedMathActivity.Operation4com.leapiq.braintraining.ui.games.StroopTestActivity6com.leapiq.braintraining.ui.games.TowerOfHanoiActivity2com.leapiq.braintraining.ui.games.TubeSortActivity6com.leapiq.braintraining.ui.games.VisualSearchActivityGcom.leapiq.braintraining.ui.games.VisualSearchActivity.SearchDifficulty4com.leapiq.braintraining.ui.games.VocabularyActivityCcom.leapiq.braintraining.ui.games.VocabularyActivity.VocabularyMode9com.leapiq.braintraining.ui.games.WordAssociationActivity4com.leapiq.braintraining.ui.games.WordSearchActivity5com.leapiq.braintraining.ui.games.adapter.GameAdapterDcom.leapiq.braintraining.ui.games.adapter.GameAdapter.GameViewHolder;com.leapiq.braintraining.ui.games.adapter.HanoiTowerAdapterKcom.leapiq.braintraining.ui.games.adapter.HanoiTowerAdapter.TowerViewHolder<com.leapiq.braintraining.ui.games.adapter.PatternGridAdapterRcom.leapiq.braintraining.ui.games.adapter.PatternGridAdapter.PatternCellViewHolder5com.leapiq.braintraining.ui.games.adapter.TubeAdapterDcom.leapiq.braintraining.ui.games.adapter.TubeAdapter.TubeViewHolder=com.leapiq.braintraining.ui.games.adapter.VisualSearchAdapterRcom.leapiq.braintraining.ui.games.adapter.VisualSearchAdapter.SearchItemViewHolder9com.leapiq.braintraining.ui.games.adapter.WordListAdapterHcom.leapiq.braintraining.ui.games.adapter.WordListAdapter.WordViewHolder?com.leapiq.braintraining.ui.games.adapter.WordSearchGridAdapterRcom.leapiq.braintraining.ui.games.adapter.WordSearchGridAdapter.GridCellViewHolder:com.leapiq.braintraining.ui.games.memory.MemoryCardAdapterIcom.leapiq.braintraining.ui.games.memory.MemoryCardAdapter.CardViewHolder2com.leapiq.braintraining.ui.games.model.Ball.Color;com.leapiq.braintraining.ui.games.model.HanoiDisk.DiskColor5com.leapiq.braintraining.ui.progress.ProgressFragment?com.leapiq.braintraining.ui.progress.adapter.AchievementAdapterUcom.leapiq.braintraining.ui.progress.adapter.AchievementAdapter.AchievementViewHolderDcom.leapiq.braintraining.ui.progress.adapter.CategoryAccuracyAdapterWcom.leapiq.braintraining.ui.progress.adapter.CategoryAccuracyAdapter.AccuracyViewHolder7com.leapiq.braintraining.ui.tests.AttentionTestActivityIcom.leapiq.braintraining.ui.tests.AttentionTestActivity.AttentionTaskType2com.leapiq.braintraining.ui.tests.BaseTestActivity7com.leapiq.braintraining.ui.tests.LearningStyleActivityJcom.leapiq.braintraining.ui.tests.LearningStyleActivity.LearningPreference:com.leapiq.braintraining.ui.tests.MemoryAssessmentActivityIcom.leapiq.braintraining.ui.tests.MemoryAssessmentActivity.MemoryTaskType=com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivityPcom.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity.ProblemSolvingTypeJcom.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity.QuestionType9com.leapiq.braintraining.ui.tests.ProcessingSpeedActivityGcom.leapiq.braintraining.ui.tests.ProcessingSpeedActivity.SpeedTaskType8com.leapiq.braintraining.ui.tests.StressResponseActivityKcom.leapiq.braintraining.ui.tests.StressResponseActivity.StressResponseType/com.leapiq.braintraining.ui.tests.TestsFragment5com.leapiq.braintraining.ui.tests.adapter.TestAdapterDcom.leapiq.braintraining.ui.tests.adapter.TestAdapter.TestViewHolderGcom.leapiq.braintraining.ui.tests.adapters.LearningStyleQuestionAdapterXcom.leapiq.braintraining.ui.tests.adapters.LearningStyleQuestionAdapter.OptionViewHolder<com.leapiq.braintraining.ui.tests.adapters.MemoryGridAdapterKcom.leapiq.braintraining.ui.tests.adapters.MemoryGridAdapter.GridViewHolderHcom.leapiq.braintraining.ui.tests.adapters.ProblemSolvingQuestionAdapterYcom.leapiq.braintraining.ui.tests.adapters.ProblemSolvingQuestionAdapter.OptionViewHolderHcom.leapiq.braintraining.ui.tests.adapters.StressResponseQuestionAdapterYcom.leapiq.braintraining.ui.tests.adapters.StressResponseQuestionAdapter.OptionViewHolder<com.leapiq.braintraining.ui.tests.results.AllResultsActivity;com.leapiq.braintraining.ui.tests.results.AnalyticsActivity;com.leapiq.braintraining.ui.tests.results.MyResultsActivity>com.leapiq.braintraining.ui.tests.results.TestProgressActivity=com.leapiq.braintraining.ui.tests.results.TestResultsActivityDcom.leapiq.braintraining.ui.tests.results.adapters.AllResultsAdapterYcom.leapiq.braintraining.ui.tests.results.adapters.AllResultsAdapter.AllResultsViewHolderQcom.leapiq.braintraining.ui.tests.results.adapters.AllResultsAdapter.DiffCallbackHcom.leapiq.braintraining.ui.tests.results.adapters.RecentActivityAdapteracom.leapiq.braintraining.ui.tests.results.adapters.RecentActivityAdapter.RecentActivityViewHolderUcom.leapiq.braintraining.ui.tests.results.adapters.RecentActivityAdapter.DiffCallbackFcom.leapiq.braintraining.ui.tests.results.adapters.TestProgressAdapter]com.leapiq.braintraining.ui.tests.results.adapters.TestProgressAdapter.TestProgressViewHolderScom.leapiq.braintraining.ui.tests.results.adapters.TestProgressAdapter.DiffCallbackEcom.leapiq.braintraining.ui.tests.results.adapters.TestResultsAdapterZcom.leapiq.braintraining.ui.tests.results.adapters.TestResultsAdapter.TestResultViewHolderRcom.leapiq.braintraining.ui.tests.results.adapters.TestResultsAdapter.DiffCallback/com.leapiq.braintraining.ui.today.TodayFragment?com.leapiq.braintraining.ui.today.adapter.DailyChallengeAdapterScom.leapiq.braintraining.ui.today.adapter.DailyChallengeAdapter.ChallengeViewHolderDcom.leapiq.braintraining.databinding.ItemStressResponseOptionBinding=com.leapiq.braintraining.databinding.ActivitySpeedMathBinding=com.leapiq.braintraining.databinding.ActivityAnalyticsBinding?<EMAIL>><EMAIL><com.leapiq.braintraining.databinding.FragmentProgressBinding>com.leapiq.braintraining.databinding.ActivityAllResultsBinding8com.leapiq.braintraining.databinding.ItemGameCardBinding>com.leapiq.braintraining.databinding.ItemDailyChallengeBindingDcom.leapiq.braintraining.databinding.ActivityMentalArithmeticBinding;<EMAIL>>com.leapiq.braintraining.databinding.ActivityGameResultBindingGcom.leapiq.braintraining.databinding.ActivityProblemSolvingStyleBindingCcom.leapiq.braintraining.databinding.ItemLearningStyleOptionBindingCcom.leapiq.braintraining.databinding.ActivityProcessingSpeedBinding8com.leapiq.braintraining.databinding.ItemTestCardBinding>com.leapiq.braintraining.databinding.ActivityWordSearchBindingBcom.leapiq.braintraining.databinding.ActivityFocusChallengeBindingDcom.leapiq.braintraining.databinding.ItemProblemSolvingOptionBinding>com.leapiq.braintraining.databinding.ItemRecentActivityBinding=com.leapiq.braintraining.databinding.ActivityMyResultsBindingCcom.leapiq.braintraining.databinding.ActivityNumberSequencesBindingBcom.leapiq.braintraining.databinding.ActivityStressResponseBindingAcom.leapiq.braintraining.databinding.ActivityPatternMemoryBindingEcom.leapiq.braintraining.databinding.ActivityPatternCompletionBindingCcom.leapiq.braintraining.databinding.ActivitySpatialRotationBinding><EMAIL>>com.leapiq.braintraining.databinding.ActivityEstimationBinding9com.leapiq.braintraining.databinding.FragmentTodayBinding<com.leapiq.braintraining.databinding.ActivityAnagramsBinding:<EMAIL>@<EMAIL>:com.leapiq.braintraining.databinding.ItemTestResultBindingAcom.leapiq.braintraining.databinding.ActivityLearningStyleBinding8com.leapiq.braintraining.databinding.ActivityMainBindingBcom.leapiq.braintraining.databinding.ActivitySequenceRecallBinding:com.leapiq.braintraining.databinding.ItemAllResultsBindingDcom.leapiq.braintraining.databinding.ActivityLogicalReasoningBinding<com.leapiq.braintraining.databinding.ItemTestProgressBinding<com.leapiq.braintraining.databinding.ActivityTubeSortBinding4com.leapiq.braintraining.ui.tests.LearningPreference4com.leapiq.braintraining.ui.tests.ProblemSolvingType.com.leapiq.braintraining.ui.tests.QuestionType3com.leapiq.braintraining.ui.profile.ProfileFragment4com.leapiq.braintraining.ui.tests.StressResponseTypeHcom.leapiq.braintraining.ui.tests.results.adapters.DetailedScoresAdapter`com.leapiq.braintraining.ui.tests.results.adapters.DetailedScoresAdapter.DetailedScoreViewHolder=com.leapiq.braintraining.databinding.ItemDetailedScoreBinding;com.leapiq.braintraining.databinding.FragmentProfileBinding6com.leapiq.braintraining.ui.games.LogicPuzzlesActivity6com.leapiq.braintraining.ui.games.adapter.CluesAdapterEcom.leapiq.braintraining.ui.games.adapter.CluesAdapter.ClueViewHolder:com.leapiq.braintraining.ui.games.adapter.LogicGridAdapterLcom.leapiq.braintraining.ui.games.adapter.LogicGridAdapter.GridRowViewHolder0com.leapiq.braintraining.ui.games.model.ClueType1com.leapiq.braintraining.ui.games.model.CellState6com.leapiq.braintraining.ui.games.model.ConstraintType0com.leapiq.braintraining.ui.games.model.HintType=com.leapiq.braintraining.databinding.ItemLogicGridCellBinding?com.leapiq.braintraining.databinding.ItemLogicGridHeaderBinding<<EMAIL>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                