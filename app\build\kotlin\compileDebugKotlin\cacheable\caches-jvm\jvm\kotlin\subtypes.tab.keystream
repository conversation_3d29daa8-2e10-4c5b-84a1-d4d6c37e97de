*com.leapiq.braintraining.data.BackupResult+com.leapiq.braintraining.data.RestoreResult*com.leapiq.braintraining.data.ExportResult2com.leapiq.braintraining.ui.tests.BaseTestActivitykotlin.Enum1androidx.recyclerview.widget.RecyclerView.Adapter(androidx.appcompat.app.AppCompatActivity2androidx.recyclerview.widget.DiffUtil.ItemCallbackandroidx.fragment.app.Fragment(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.viewbinding.ViewBinding6com.leapiq.braintraining.ui.tests.BaseTestInfoActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            