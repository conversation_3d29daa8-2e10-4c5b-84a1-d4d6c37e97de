package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivitySequenceRecallBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date

/**
 * Sequence Recall Memory Game (Simon-style)
 * Players watch a sequence of colored buttons light up, then repeat the sequence
 * Difficulty increases with level (longer sequences)
 */
class SequenceRecallActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySequenceRecallBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "memory_2"

    // Sequence game specific
    private var gameSequence = mutableListOf<Int>()
    private var playerSequence = mutableListOf<Int>()
    private var isShowingSequence = false
    private var isPlayerTurn = false
    private var sequenceIndex = 0

    // Button IDs for the colored buttons
    private val buttonIds = listOf(
        R.id.btn_red,
        R.id.btn_blue, 
        R.id.btn_green,
        R.id.btn_yellow
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySequenceRecallBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.sequence_recall)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Watch the sequence, then repeat it!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup colored buttons
            setupColorButtons()
        }
    }

    private fun setupColorButtons() {
        binding.apply {
            btnRed.setOnClickListener { onColorButtonClicked(0) }
            btnBlue.setOnClickListener { onColorButtonClicked(1) }
            btnGreen.setOnClickListener { onColorButtonClicked(2) }
            btnYellow.setOnClickListener { onColorButtonClicked(3) }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        generateSequence()
        showSequence()
    }

    private fun generateSequence() {
        gameSequence.clear()
        val sequenceLength = getSequenceLength(currentLevel)
        
        repeat(sequenceLength) {
            gameSequence.add((0..3).random())
        }
    }

    private fun getSequenceLength(level: Int): Int {
        return when (level) {
            in 1..5 -> 3 + (level - 1) // 3, 4, 5, 6, 7
            in 6..10 -> 7 + (level - 6) // 8, 9, 10, 11, 12
            in 11..15 -> 12 + (level - 11) // 13, 14, 15, 16, 17
            else -> 17 + (level - 16) // 18+
        }
    }

    private fun showSequence() {
        isShowingSequence = true
        isPlayerTurn = false
        sequenceIndex = 0
        playerSequence.clear()
        
        binding.instructionText.text = "Watch the sequence..."
        disableAllButtons()
        
        showNextSequenceItem()
    }

    private fun showNextSequenceItem() {
        if (sequenceIndex >= gameSequence.size) {
            // Sequence complete, player's turn
            isShowingSequence = false
            isPlayerTurn = true
            binding.instructionText.text = "Now repeat the sequence!"
            enableAllButtons()
            return
        }

        val buttonIndex = gameSequence[sequenceIndex]
        highlightButton(buttonIndex)
        
        Handler(Looper.getMainLooper()).postDelayed({
            unhighlightButton(buttonIndex)
            sequenceIndex++
            
            Handler(Looper.getMainLooper()).postDelayed({
                showNextSequenceItem()
            }, 300) // Pause between items
        }, 600) // Button highlight duration
    }

    private fun highlightButton(buttonIndex: Int) {
        val button = getButtonByIndex(buttonIndex)
        button?.alpha = 1.0f
        button?.scaleX = 1.1f
        button?.scaleY = 1.1f
    }

    private fun unhighlightButton(buttonIndex: Int) {
        val button = getButtonByIndex(buttonIndex)
        button?.alpha = 0.7f
        button?.scaleX = 1.0f
        button?.scaleY = 1.0f
    }

    private fun getButtonByIndex(index: Int): Button? {
        return when (index) {
            0 -> binding.btnRed
            1 -> binding.btnBlue
            2 -> binding.btnGreen
            3 -> binding.btnYellow
            else -> null
        }
    }

    private fun onColorButtonClicked(buttonIndex: Int) {
        if (!isPlayerTurn) return

        // Visual feedback
        highlightButton(buttonIndex)
        Handler(Looper.getMainLooper()).postDelayed({
            unhighlightButton(buttonIndex)
        }, 200)

        playerSequence.add(buttonIndex)
        
        // Check if sequence matches so far
        if (playerSequence.size <= gameSequence.size) {
            val currentIndex = playerSequence.size - 1
            if (playerSequence[currentIndex] != gameSequence[currentIndex]) {
                // Wrong button - end round
                roundFailed()
                return
            }
        }

        // Check if sequence is complete
        if (playerSequence.size == gameSequence.size) {
            // Sequence complete and correct
            roundComplete()
        }
    }

    private fun roundComplete() {
        totalCorrect++
        totalAttempts++
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = "Correct! Round $currentRound starting..."
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            Handler(Looper.getMainLooper()).postDelayed({
                setupGame()
            }, 1500)
        }
    }

    private fun roundFailed() {
        totalAttempts++
        
        binding.instructionText.text = "Wrong sequence! Try again..."
        disableAllButtons()
        
        Handler(Looper.getMainLooper()).postDelayed({
            showSequence()
        }, 1500)
    }

    private fun disableAllButtons() {
        binding.apply {
            btnRed.isEnabled = false
            btnBlue.isEnabled = false
            btnGreen.isEnabled = false
            btnYellow.isEnabled = false
            
            btnRed.alpha = 0.5f
            btnBlue.alpha = 0.5f
            btnGreen.alpha = 0.5f
            btnYellow.alpha = 0.5f
        }
    }

    private fun enableAllButtons() {
        binding.apply {
            btnRed.isEnabled = true
            btnBlue.isEnabled = true
            btnGreen.isEnabled = true
            btnYellow.isEnabled = true
            
            btnRed.alpha = 0.7f
            btnBlue.alpha = 0.7f
            btnGreen.alpha = 0.7f
            btnYellow.alpha = 0.7f
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = i <= totalCorrect,
                timeSpentMs = totalTime / maxRounds,
                attempts = 1
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Time: ${totalTime / 1000}s
                Sequence Length: ${getSequenceLength(currentLevel)}

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Time: ${totalTime / 1000}s

                Memory Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Remember and repeat color sequences

                📋 RULES:
                • Watch the sequence of colored buttons
                • Repeat the sequence in the same order
                • Complete 3 rounds to advance to next level
                • Sequences get longer each level

                💡 TIPS:
                • Focus on the pattern
                • Use memory techniques
                • Stay calm and concentrated

                🏆 SCORING:
                • Accuracy = correct sequences / total attempts
                • Faster completion = better score
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
