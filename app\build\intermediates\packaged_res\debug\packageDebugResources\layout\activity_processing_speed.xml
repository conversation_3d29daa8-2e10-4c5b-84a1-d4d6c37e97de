<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_white"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="Processing Speed Test"
        app:titleTextColor="@color/text_primary" />

    <!-- Progress Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/surface_white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/progress_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Trial 1 of 20"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/task_type_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Simple Reaction Time"
                android:textColor="@color/primary_light_blue"
                android:textSize="14sp" />

        </LinearLayout>

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="8dp"
            android:progressTint="@color/primary_light_blue" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="Respond as quickly and accurately as possible to each stimulus."
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:gravity="center"
        android:background="@drawable/rounded_background"
        android:padding="16dp" />

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Feedback Text -->
            <TextView
                android:id="@+id/feedback_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text=""
                android:textColor="@color/success_green"
                android:textSize="18sp"
                android:textStyle="bold"
                android:gravity="center"
                android:visibility="gone" />

            <!-- Simple Reaction Task -->
            <LinearLayout
                android:id="@+id/simple_reaction_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/stimulus_display"
                    android:layout_width="200dp"
                    android:layout_height="200dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="32dp"
                    android:gravity="center"
                    android:text="Wait..."
                    android:textColor="@color/text_secondary"
                    android:textSize="72sp"
                    android:textStyle="bold"
                    android:background="@drawable/rounded_background" />

                <Button
                    android:id="@+id/simple_response_button"
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:text="RESPOND"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/primary_light_blue"
                    android:textColor="@color/surface_white" />

            </LinearLayout>

            <!-- Choice Reaction Task -->
            <LinearLayout
                android:id="@+id/choice_reaction_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/choice_stimulus"
                    android:layout_width="200dp"
                    android:layout_height="120dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="32dp"
                    android:gravity="center"
                    android:text="Get Ready"
                    android:textColor="@color/text_secondary"
                    android:textSize="48sp"
                    android:textStyle="bold"
                    android:background="@drawable/rounded_background" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="4">

                    <Button
                        android:id="@+id/red_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="RED"
                        android:textSize="12sp"
                        app:backgroundTint="@color/stroop_red"
                        android:textColor="@color/surface_white" />

                    <Button
                        android:id="@+id/blue_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="BLUE"
                        android:textSize="12sp"
                        app:backgroundTint="@color/stroop_blue"
                        android:textColor="@color/surface_white" />

                    <Button
                        android:id="@+id/green_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="GREEN"
                        android:textSize="12sp"
                        app:backgroundTint="@color/stroop_green"
                        android:textColor="@color/surface_white" />

                    <Button
                        android:id="@+id/yellow_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="YELLOW"
                        android:textSize="12sp"
                        app:backgroundTint="@color/stroop_yellow"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

            <!-- Symbol Coding Task -->
            <LinearLayout
                android:id="@+id/symbol_coding_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- Symbol Mapping Reference -->
                <TextView
                    android:id="@+id/symbol_mapping_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@ = 1  # = 2  % = 3  &amp; = 4"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:background="@drawable/rounded_background"
                    android:padding="12dp" />

                <TextView
                    android:id="@+id/symbol_display"
                    android:layout_width="200dp"
                    android:layout_height="120dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="32dp"
                    android:gravity="center"
                    android:text="Ready"
                    android:textColor="@color/text_secondary"
                    android:textSize="72sp"
                    android:textStyle="bold"
                    android:background="@drawable/rounded_background" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="4">

                    <Button
                        android:id="@+id/symbol_1_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="1"
                        android:textSize="18sp"
                        app:backgroundTint="@color/surface_white"
                        android:textColor="@color/text_primary" />

                    <Button
                        android:id="@+id/symbol_2_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="2"
                        android:textSize="18sp"
                        app:backgroundTint="@color/surface_white"
                        android:textColor="@color/text_primary" />

                    <Button
                        android:id="@+id/symbol_3_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="3"
                        android:textSize="18sp"
                        app:backgroundTint="@color/surface_white"
                        android:textColor="@color/text_primary" />

                    <Button
                        android:id="@+id/symbol_4_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:text="4"
                        android:textSize="18sp"
                        app:backgroundTint="@color/surface_white"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

            <!-- Decision Speed Task -->
            <LinearLayout
                android:id="@+id/decision_speed_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/decision_text"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="32dp"
                    android:gravity="center"
                    android:text="Get Ready"
                    android:textColor="@color/text_secondary"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:background="@drawable/rounded_background"
                    android:padding="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <Button
                        android:id="@+id/yes_button"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_margin="8dp"
                        android:text="YES"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        app:backgroundTint="@color/success_green"
                        android:textColor="@color/surface_white" />

                    <Button
                        android:id="@+id/no_button"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_margin="8dp"
                        android:text="NO"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        app:backgroundTint="@color/error_red"
                        android:textColor="@color/surface_white" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
