<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_number_memory" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_number_memory.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_number_memory_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="295" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="91" startOffset="4" endLine="100" endOffset="33"/></Target><Target id="@+id/number_display" view="TextView"><Expressions/><location startLine="110" startOffset="8" endLine="121" endOffset="36"/></Target><Target id="@+id/input_display" view="TextView"><Expressions/><location startLine="124" startOffset="8" endLine="134" endOffset="38"/></Target><Target id="@+id/number_pad_container" view="LinearLayout"><Expressions/><location startLine="139" startOffset="4" endLine="293" endOffset="18"/></Target><Target id="@+id/btn_1" view="Button"><Expressions/><location startLine="156" startOffset="12" endLine="165" endOffset="40"/></Target><Target id="@+id/btn_2" view="Button"><Expressions/><location startLine="167" startOffset="12" endLine="176" endOffset="40"/></Target><Target id="@+id/btn_3" view="Button"><Expressions/><location startLine="178" startOffset="12" endLine="187" endOffset="40"/></Target><Target id="@+id/btn_4" view="Button"><Expressions/><location startLine="190" startOffset="12" endLine="199" endOffset="40"/></Target><Target id="@+id/btn_5" view="Button"><Expressions/><location startLine="201" startOffset="12" endLine="210" endOffset="40"/></Target><Target id="@+id/btn_6" view="Button"><Expressions/><location startLine="212" startOffset="12" endLine="221" endOffset="40"/></Target><Target id="@+id/btn_7" view="Button"><Expressions/><location startLine="224" startOffset="12" endLine="233" endOffset="40"/></Target><Target id="@+id/btn_8" view="Button"><Expressions/><location startLine="235" startOffset="12" endLine="244" endOffset="40"/></Target><Target id="@+id/btn_9" view="Button"><Expressions/><location startLine="246" startOffset="12" endLine="255" endOffset="40"/></Target><Target id="@+id/btn_clear" view="Button"><Expressions/><location startLine="258" startOffset="12" endLine="267" endOffset="40"/></Target><Target id="@+id/btn_0" view="Button"><Expressions/><location startLine="269" startOffset="12" endLine="278" endOffset="40"/></Target><Target id="@+id/btn_submit" view="Button"><Expressions/><location startLine="280" startOffset="12" endLine="289" endOffset="40"/></Target></Targets></Layout>