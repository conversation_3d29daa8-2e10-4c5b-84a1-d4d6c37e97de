package com.leapiq.braintraining.ui.tests

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityProcessingSpeedBinding
import com.leapiq.braintraining.data.model.TestType
import kotlin.random.Random

/**
 * Processing Speed Test - Response time evaluation
 * Tests simple reaction time, choice reaction time, symbol coding, and decision speed
 * Dynamic timing based on speed-accuracy trade-off
 */
class ProcessingSpeedActivity : BaseTestActivity() {
    
    private lateinit var binding: ActivityProcessingSpeedBinding
    
    // Test configuration
    private var currentTaskType = SpeedTaskType.SIMPLE_REACTION
    private var currentTrial = 0
    private var trialsPerTask = 20 // Dynamic - can adjust based on performance
    private val totalTasks = 4 // Different processing speed task types
    
    // Current trial data
    private var trialStartTime = 0L
    private var stimulusAppearTime = 0L
    private var currentStimulus = ""
    private var correctResponse = ""
    private var isWaitingForStimulus = false
    private var isWaitingForResponse = false
    
    // Performance tracking
    private val reactionTimes = mutableListOf<Long>()
    private val accuracyScores = mutableListOf<Boolean>()
    private var falseStarts = 0
    private var timeouts = 0
    
    // Task-specific scores
    private var simpleReactionScore = 0.0
    private var choiceReactionScore = 0.0
    private var symbolCodingScore = 0.0
    private var decisionSpeedScore = 0.0
    
    // Processing speed task types
    private enum class SpeedTaskType {
        SIMPLE_REACTION,    // React to single stimulus
        CHOICE_REACTION,    // Choose between multiple stimuli
        SYMBOL_CODING,      // Match symbols to numbers
        DECISION_SPEED      // Quick decision making
    }
    
    // Stimulus sets
    private val colors = listOf("RED", "BLUE", "GREEN", "YELLOW")
    private val colorResources = listOf(R.color.stroop_red, R.color.stroop_blue, R.color.stroop_green, R.color.stroop_yellow)
    private val shapes = listOf("●", "■", "▲", "♦")
    private val symbols = listOf("@", "#", "%", "&", "*", "+", "=", "~")
    private val numbers = listOf("1", "2", "3", "4", "5", "6", "7", "8")
    
    // Symbol-number mappings for coding task
    private val symbolMappings = symbols.zip(numbers).toMap()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProcessingSpeedBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }
    
    override fun initializeTest() {
        totalQuestions = totalTasks * trialsPerTask
        setupUI()
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            
            // Setup progress
            progressBar.max = totalQuestions
            
            // Setup response buttons
            setupResponseButtons()
            
            // Setup symbol coding reference
            setupSymbolCodingReference()
        }
    }
    
    private fun setupResponseButtons() {
        binding.apply {
            // Simple reaction button
            simpleResponseButton.setOnClickListener { onSimpleResponse() }
            
            // Choice reaction buttons
            redButton.setOnClickListener { onChoiceResponse("RED") }
            blueButton.setOnClickListener { onChoiceResponse("BLUE") }
            greenButton.setOnClickListener { onChoiceResponse("GREEN") }
            yellowButton.setOnClickListener { onChoiceResponse("YELLOW") }
            
            // Symbol coding buttons
            symbol1Button.setOnClickListener { onSymbolResponse("1") }
            symbol2Button.setOnClickListener { onSymbolResponse("2") }
            symbol3Button.setOnClickListener { onSymbolResponse("3") }
            symbol4Button.setOnClickListener { onSymbolResponse("4") }
            
            // Decision buttons
            yesButton.setOnClickListener { onDecisionResponse("YES") }
            noButton.setOnClickListener { onDecisionResponse("NO") }
        }
    }
    
    private fun setupSymbolCodingReference() {
        binding.apply {
            // Show symbol-number mappings
            val mappingText = symbolMappings.entries.take(4).joinToString("  ") { "${it.key} = ${it.value}" }
            symbolMappingText.text = mappingText
        }
    }
    
    override fun startQuestion() {
        updateProgress()
        selectCurrentTaskType()
        startTrialSequence()
    }
    
    private fun updateProgress() {
        binding.apply {
            progressBar.progress = currentQuestion - 1
            progressText.text = "Trial ${currentTrial + 1} of ${trialsPerTask}"
            taskTypeText.text = getTaskTypeDescription(currentTaskType)
        }
    }
    
    private fun selectCurrentTaskType() {
        currentTaskType = when ((currentQuestion - 1) / trialsPerTask) {
            0 -> SpeedTaskType.SIMPLE_REACTION
            1 -> SpeedTaskType.CHOICE_REACTION
            2 -> SpeedTaskType.SYMBOL_CODING
            3 -> SpeedTaskType.DECISION_SPEED
            else -> SpeedTaskType.SIMPLE_REACTION
        }
        
        // Reset trial counter for new task type
        if ((currentQuestion - 1) % trialsPerTask == 0) {
            currentTrial = 0
            setupTaskUI()
        }
    }
    
    private fun setupTaskUI() {
        binding.apply {
            // Hide all task containers
            simpleReactionContainer.visibility = View.GONE
            choiceReactionContainer.visibility = View.GONE
            symbolCodingContainer.visibility = View.GONE
            decisionSpeedContainer.visibility = View.GONE
            
            // Show current task container
            when (currentTaskType) {
                SpeedTaskType.SIMPLE_REACTION -> {
                    simpleReactionContainer.visibility = View.VISIBLE
                    instructionText.text = "Click the button as soon as the circle appears"
                }
                SpeedTaskType.CHOICE_REACTION -> {
                    choiceReactionContainer.visibility = View.VISIBLE
                    instructionText.text = "Click the button matching the color shown"
                }
                SpeedTaskType.SYMBOL_CODING -> {
                    symbolCodingContainer.visibility = View.VISIBLE
                    instructionText.text = "Enter the number that matches the symbol"
                }
                SpeedTaskType.DECISION_SPEED -> {
                    decisionSpeedContainer.visibility = View.VISIBLE
                    instructionText.text = "Decide if the statement is true or false"
                }
            }
        }
    }
    
    private fun startTrialSequence() {
        // Reset trial state
        isWaitingForStimulus = false
        isWaitingForResponse = false
        
        // Prepare trial
        generateTrial()
        
        // Show ready state
        showReadyState()
        
        // Start trial after random delay (1-4 seconds)
        val delay = Random.nextLong(1000, 4000)
        Handler(Looper.getMainLooper()).postDelayed({
            showStimulus()
        }, delay)
    }
    
    private fun generateTrial() {
        when (currentTaskType) {
            SpeedTaskType.SIMPLE_REACTION -> {
                currentStimulus = "●"
                correctResponse = "RESPOND"
            }
            SpeedTaskType.CHOICE_REACTION -> {
                val colorIndex = Random.nextInt(colors.size)
                currentStimulus = colors[colorIndex]
                correctResponse = currentStimulus
            }
            SpeedTaskType.SYMBOL_CODING -> {
                currentStimulus = symbols.take(4).random()
                correctResponse = symbolMappings[currentStimulus] ?: "1"
            }
            SpeedTaskType.DECISION_SPEED -> {
                generateDecisionTask()
            }
        }
    }
    
    private fun generateDecisionTask() {
        val taskTypes = listOf("math", "logic", "comparison")
        val taskType = taskTypes.random()
        
        when (taskType) {
            "math" -> {
                val a = Random.nextInt(1, 20)
                val b = Random.nextInt(1, 20)
                val result = Random.nextInt(1, 40)
                val isCorrect = (a + b) == result
                currentStimulus = "$a + $b = $result"
                correctResponse = if (isCorrect) "YES" else "NO"
            }
            "logic" -> {
                val statements = listOf(
                    "All birds can fly" to "NO",
                    "Water boils at 100°C" to "YES",
                    "There are 7 days in a week" to "YES",
                    "Cats are reptiles" to "NO"
                )
                val (statement, answer) = statements.random()
                currentStimulus = statement
                correctResponse = answer
            }
            "comparison" -> {
                val a = Random.nextInt(10, 99)
                val b = Random.nextInt(10, 99)
                val isGreater = a > b
                currentStimulus = "$a > $b"
                correctResponse = if (isGreater) "YES" else "NO"
            }
        }
    }
    
    private fun showReadyState() {
        isWaitingForStimulus = true
        trialStartTime = System.currentTimeMillis()
        
        binding.apply {
            when (currentTaskType) {
                SpeedTaskType.SIMPLE_REACTION -> {
                    stimulusDisplay.text = "Wait..."
                    stimulusDisplay.setTextColor(ContextCompat.getColor(this@ProcessingSpeedActivity, R.color.text_secondary))
                    simpleResponseButton.isEnabled = true
                }
                SpeedTaskType.CHOICE_REACTION -> {
                    choiceStimulus.text = "Get Ready"
                    choiceStimulus.setTextColor(ContextCompat.getColor(this@ProcessingSpeedActivity, R.color.text_secondary))
                    enableChoiceButtons(true)
                }
                SpeedTaskType.SYMBOL_CODING -> {
                    symbolDisplay.text = "Ready"
                    symbolDisplay.setTextColor(ContextCompat.getColor(this@ProcessingSpeedActivity, R.color.text_secondary))
                    enableSymbolButtons(true)
                }
                SpeedTaskType.DECISION_SPEED -> {
                    decisionText.text = "Get Ready"
                    decisionText.setTextColor(ContextCompat.getColor(this@ProcessingSpeedActivity, R.color.text_secondary))
                    enableDecisionButtons(true)
                }
            }
        }
    }
    
    private fun showStimulus() {
        if (!isWaitingForStimulus) return // Prevent if user already responded
        
        isWaitingForStimulus = false
        isWaitingForResponse = true
        stimulusAppearTime = System.currentTimeMillis()
        
        binding.apply {
            when (currentTaskType) {
                SpeedTaskType.SIMPLE_REACTION -> {
                    stimulusDisplay.text = currentStimulus
                    stimulusDisplay.setTextColor(ContextCompat.getColor(this@ProcessingSpeedActivity, R.color.primary_light_blue))
                }
                SpeedTaskType.CHOICE_REACTION -> {
                    choiceStimulus.text = currentStimulus
                    val colorIndex = colors.indexOf(currentStimulus)
                    if (colorIndex >= 0) {
                        choiceStimulus.setTextColor(ContextCompat.getColor(this@ProcessingSpeedActivity, colorResources[colorIndex]))
                    }
                }
                SpeedTaskType.SYMBOL_CODING -> {
                    symbolDisplay.text = currentStimulus
                    symbolDisplay.setTextColor(ContextCompat.getColor(this@ProcessingSpeedActivity, R.color.text_primary))
                }
                SpeedTaskType.DECISION_SPEED -> {
                    decisionText.text = currentStimulus
                    decisionText.setTextColor(ContextCompat.getColor(this@ProcessingSpeedActivity, R.color.text_primary))
                }
            }
        }
        
        // Set timeout for response
        Handler(Looper.getMainLooper()).postDelayed({
            if (isWaitingForResponse) {
                onTrialTimeout()
            }
        }, 3000) // 3 second timeout
    }
    
    private fun onSimpleResponse() {
        if (isWaitingForStimulus) {
            // False start
            falseStarts++
            showFeedback("Too early!", R.color.error_red)
            nextTrial()
        } else if (isWaitingForResponse) {
            val reactionTime = System.currentTimeMillis() - stimulusAppearTime
            recordResponse(reactionTime, true)
        }
    }
    
    private fun onChoiceResponse(response: String) {
        if (isWaitingForStimulus) {
            falseStarts++
            showFeedback("Too early!", R.color.error_red)
            nextTrial()
        } else if (isWaitingForResponse) {
            val reactionTime = System.currentTimeMillis() - stimulusAppearTime
            val isCorrect = response == correctResponse
            recordResponse(reactionTime, isCorrect)
        }
    }
    
    private fun onSymbolResponse(response: String) {
        if (isWaitingForStimulus) {
            falseStarts++
            showFeedback("Too early!", R.color.error_red)
            nextTrial()
        } else if (isWaitingForResponse) {
            val reactionTime = System.currentTimeMillis() - stimulusAppearTime
            val isCorrect = response == correctResponse
            recordResponse(reactionTime, isCorrect)
        }
    }
    
    private fun onDecisionResponse(response: String) {
        if (isWaitingForStimulus) {
            falseStarts++
            showFeedback("Too early!", R.color.error_red)
            nextTrial()
        } else if (isWaitingForResponse) {
            val reactionTime = System.currentTimeMillis() - stimulusAppearTime
            val isCorrect = response == correctResponse
            recordResponse(reactionTime, isCorrect)
        }
    }
    
    private fun recordResponse(reactionTime: Long, isCorrect: Boolean) {
        isWaitingForResponse = false
        
        reactionTimes.add(reactionTime)
        accuracyScores.add(isCorrect)
        
        // Disable buttons
        disableAllButtons()
        
        // Show feedback
        val feedbackText = if (isCorrect) "Correct! ${reactionTime}ms" else "Incorrect"
        val feedbackColor = if (isCorrect) R.color.success_green else R.color.error_red
        showFeedback(feedbackText, feedbackColor)
        
        // Continue to next trial
        Handler(Looper.getMainLooper()).postDelayed({
            nextTrial()
        }, 1000)
    }
    
    private fun onTrialTimeout() {
        isWaitingForResponse = false
        timeouts++
        
        disableAllButtons()
        showFeedback("Too slow!", R.color.warning_orange)
        
        Handler(Looper.getMainLooper()).postDelayed({
            nextTrial()
        }, 1000)
    }
    
    private fun showFeedback(message: String, colorRes: Int) {
        binding.apply {
            feedbackText.text = message
            feedbackText.setTextColor(ContextCompat.getColor(this@ProcessingSpeedActivity, colorRes))
            feedbackText.visibility = View.VISIBLE
            
            Handler(Looper.getMainLooper()).postDelayed({
                feedbackText.visibility = View.GONE
            }, 800)
        }
    }
    
    private fun nextTrial() {
        currentTrial++
        
        if (currentTrial >= trialsPerTask) {
            // Task complete, update scores
            updateTaskScores()
            
            // Move to next question/task
            val avgAccuracy = if (accuracyScores.isNotEmpty()) {
                accuracyScores.count { it }.toDouble() / accuracyScores.size
            } else 0.0
            
            val isCorrect = avgAccuracy > 0.7 // 70% accuracy threshold
            completeQuestion(isCorrect = isCorrect)
        } else {
            // Continue with next trial
            startTrialSequence()
        }
    }
    
    private fun updateTaskScores() {
        val avgRT = if (reactionTimes.isNotEmpty()) reactionTimes.average() else 1000.0
        val accuracy = if (accuracyScores.isNotEmpty()) {
            accuracyScores.count { it }.toDouble() / accuracyScores.size
        } else 0.0
        
        // Score based on speed-accuracy trade-off
        val speedScore = maxOf(0.0, 1.0 - (avgRT - 200) / 1000) // Normalize RT to 0-1
        val combinedScore = (speedScore * 0.6 + accuracy * 0.4) * 100 // Weight speed 60%, accuracy 40%
        
        when (currentTaskType) {
            SpeedTaskType.SIMPLE_REACTION -> simpleReactionScore = combinedScore
            SpeedTaskType.CHOICE_REACTION -> choiceReactionScore = combinedScore
            SpeedTaskType.SYMBOL_CODING -> symbolCodingScore = combinedScore
            SpeedTaskType.DECISION_SPEED -> decisionSpeedScore = combinedScore
        }
        
        // Reset counters for next task
        reactionTimes.clear()
        accuracyScores.clear()
    }
    
    private fun enableChoiceButtons(enabled: Boolean) {
        binding.apply {
            redButton.isEnabled = enabled
            blueButton.isEnabled = enabled
            greenButton.isEnabled = enabled
            yellowButton.isEnabled = enabled
        }
    }
    
    private fun enableSymbolButtons(enabled: Boolean) {
        binding.apply {
            symbol1Button.isEnabled = enabled
            symbol2Button.isEnabled = enabled
            symbol3Button.isEnabled = enabled
            symbol4Button.isEnabled = enabled
        }
    }
    
    private fun enableDecisionButtons(enabled: Boolean) {
        binding.apply {
            yesButton.isEnabled = enabled
            noButton.isEnabled = enabled
        }
    }
    
    private fun disableAllButtons() {
        binding.apply {
            simpleResponseButton.isEnabled = false
            enableChoiceButtons(false)
            enableSymbolButtons(false)
            enableDecisionButtons(false)
        }
    }
    
    private fun getTaskTypeDescription(taskType: SpeedTaskType): String {
        return when (taskType) {
            SpeedTaskType.SIMPLE_REACTION -> "Simple Reaction Time"
            SpeedTaskType.CHOICE_REACTION -> "Choice Reaction Time"
            SpeedTaskType.SYMBOL_CODING -> "Symbol Coding Speed"
            SpeedTaskType.DECISION_SPEED -> "Decision Speed"
        }
    }
    
    override fun getTestType(): TestType = TestType.COGNITIVE
    
    override fun calculateScore(): Int {
        val totalTasks = questionResults.size
        val correctTasks = questionResults.count { it.isCorrect == true }
        return if (totalTasks > 0) (correctTasks * 100) / totalTasks else 0
    }
    
    override fun generateDetailedScores(): Map<String, Double> {
        return mapOf(
            "Simple Reaction Time" to simpleReactionScore,
            "Choice Reaction Time" to choiceReactionScore,
            "Symbol Coding Speed" to symbolCodingScore,
            "Decision Speed" to decisionSpeedScore
        )
    }
    
    override fun generateInsights(): List<String> {
        val insights = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        
        detailedScores.forEach { (type, score) ->
            when {
                score >= 80 -> insights.add("Excellent $type - very fast and accurate responses")
                score >= 60 -> insights.add("Good $type - solid performance with room for improvement")
                else -> insights.add("$type could benefit from practice to improve speed and accuracy")
            }
        }
        
        if (falseStarts > 2) {
            insights.add("Multiple false starts detected - focus on waiting for the stimulus")
        }
        
        return insights
    }
    
    override fun generateRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        
        if (detailedScores["Simple Reaction Time"]!! < 70) {
            recommendations.add("Practice simple reaction time exercises to improve basic response speed")
        }
        
        if (detailedScores["Choice Reaction Time"]!! < 70) {
            recommendations.add("Work on choice reaction tasks to improve decision-making speed")
        }
        
        if (detailedScores["Symbol Coding Speed"]!! < 70) {
            recommendations.add("Practice symbol-digit coding tasks to enhance processing speed")
        }
        
        recommendations.add("Regular physical exercise can improve overall reaction time")
        recommendations.add("Adequate sleep is crucial for optimal processing speed")
        recommendations.add("Minimize distractions when quick responses are needed")
        
        return recommendations
    }
    
    override fun showInstructions() {
        binding.instructionText.text = "Respond as quickly and accurately as possible to each stimulus."
    }
}
