// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAttentionTestBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView distractor1;

  @NonNull
  public final TextView distractor2;

  @NonNull
  public final TextView distractor3;

  @NonNull
  public final TextView distractor4;

  @NonNull
  public final TextView feedbackText;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final MaterialButton responseButton;

  @NonNull
  public final TextView stimulusDisplay;

  @NonNull
  public final TextView taskTypeText;

  @NonNull
  public final Toolbar toolbar;

  private ActivityAttentionTestBinding(@NonNull LinearLayout rootView,
      @NonNull TextView distractor1, @NonNull TextView distractor2, @NonNull TextView distractor3,
      @NonNull TextView distractor4, @NonNull TextView feedbackText,
      @NonNull TextView instructionText, @NonNull ProgressBar progressBar,
      @NonNull TextView progressText, @NonNull MaterialButton responseButton,
      @NonNull TextView stimulusDisplay, @NonNull TextView taskTypeText, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.distractor1 = distractor1;
    this.distractor2 = distractor2;
    this.distractor3 = distractor3;
    this.distractor4 = distractor4;
    this.feedbackText = feedbackText;
    this.instructionText = instructionText;
    this.progressBar = progressBar;
    this.progressText = progressText;
    this.responseButton = responseButton;
    this.stimulusDisplay = stimulusDisplay;
    this.taskTypeText = taskTypeText;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAttentionTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAttentionTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_attention_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAttentionTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.distractor_1;
      TextView distractor1 = ViewBindings.findChildViewById(rootView, id);
      if (distractor1 == null) {
        break missingId;
      }

      id = R.id.distractor_2;
      TextView distractor2 = ViewBindings.findChildViewById(rootView, id);
      if (distractor2 == null) {
        break missingId;
      }

      id = R.id.distractor_3;
      TextView distractor3 = ViewBindings.findChildViewById(rootView, id);
      if (distractor3 == null) {
        break missingId;
      }

      id = R.id.distractor_4;
      TextView distractor4 = ViewBindings.findChildViewById(rootView, id);
      if (distractor4 == null) {
        break missingId;
      }

      id = R.id.feedback_text;
      TextView feedbackText = ViewBindings.findChildViewById(rootView, id);
      if (feedbackText == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progress_text;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.response_button;
      MaterialButton responseButton = ViewBindings.findChildViewById(rootView, id);
      if (responseButton == null) {
        break missingId;
      }

      id = R.id.stimulus_display;
      TextView stimulusDisplay = ViewBindings.findChildViewById(rootView, id);
      if (stimulusDisplay == null) {
        break missingId;
      }

      id = R.id.task_type_text;
      TextView taskTypeText = ViewBindings.findChildViewById(rootView, id);
      if (taskTypeText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityAttentionTestBinding((LinearLayout) rootView, distractor1, distractor2,
          distractor3, distractor4, feedbackText, instructionText, progressBar, progressText,
          responseButton, stimulusDisplay, taskTypeText, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
