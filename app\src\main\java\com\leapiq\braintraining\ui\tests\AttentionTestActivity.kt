package com.leapiq.braintraining.ui.tests

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityAttentionTestBinding
import com.leapiq.braintraining.data.model.TestType
import kotlin.random.Random

/**
 * Sustained Attention Test using Continuous Performance Test (CPT) methodology
 * Tests sustained attention, vigilance, and distraction resistance
 * Dynamic timing based on performance consistency
 */
class AttentionTestActivity : BaseTestActivity() {
    
    private lateinit var binding: ActivityAttentionTestBinding
    
    // Test configuration
    private var currentTaskType = AttentionTaskType.SIMPLE_CPT
    private var currentTrial = 0
    private var trialsPerTask = 30 // Dynamic - can adjust based on performance
    private val totalTasks = 4 // Different attention task types
    
    // Current trial data
    private var currentStimulus = ""
    private var targetStimulus = "X"
    private var isTargetTrial = false
    private var trialStartTime = 0L
    private var stimulusDisplayTime = 500L // 500ms default
    private var interTrialInterval = 1500L // 1.5s between trials
    
    // Performance tracking
    private var hits = 0 // Correct target detections
    private var misses = 0 // Missed targets
    private var falseAlarms = 0 // Incorrect responses to non-targets
    private var correctRejections = 0 // Correct non-responses to non-targets
    private val reactionTimes = mutableListOf<Long>()
    
    // Task-specific scores
    private var sustainedAttentionScore = 0.0
    private var selectiveAttentionScore = 0.0
    private var vigilanceScore = 0.0
    private var distractorResistanceScore = 0.0
    
    // Attention task types
    private enum class AttentionTaskType {
        SIMPLE_CPT,        // Basic continuous performance test
        SELECTIVE_ATTENTION, // Attention with distractors
        VIGILANCE_TASK,    // Rare target detection
        DUAL_TASK         // Divided attention task
    }
    
    // Stimulus types
    private val letters = listOf("A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z")
    private val shapes = listOf("●", "■", "▲", "♦", "★", "◆", "▼", "◀", "▶", "♠")
    private val colors = listOf(R.color.stroop_red, R.color.stroop_blue, R.color.stroop_green, R.color.stroop_yellow, R.color.search_purple)
    
    override fun initializeUI() {
        binding = ActivityAttentionTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupUI()
    }

    override fun initializeTest() {
        totalQuestions = totalTasks * trialsPerTask
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            
            // Setup progress
            progressBar.max = totalQuestions
            
            // Setup response button
            responseButton.setOnClickListener { onResponseButtonClicked() }
            
            // Setup distractor elements (initially hidden)
            setupDistractors()
        }
    }
    
    private fun setupDistractors() {
        binding.apply {
            // Position distractors around the screen
            val distractors = listOf(distractor1, distractor2, distractor3, distractor4)
            distractors.forEachIndexed { index, distractor ->
                distractor.text = shapes[index % shapes.size]
                distractor.setTextColor(ContextCompat.getColor(this@AttentionTestActivity, colors[index % colors.size]))
                distractor.visibility = View.GONE
            }
        }
    }
    
    override fun startQuestion() {
        updateProgress()
        selectCurrentTaskType()
        startTrialSequence()
    }
    
    private fun updateProgress() {
        binding.apply {
            progressBar.progress = currentQuestion - 1
            progressText.text = "Trial ${currentTrial + 1} of ${trialsPerTask}"
            taskTypeText.text = getTaskTypeDescription(currentTaskType)
        }
    }
    
    private fun selectCurrentTaskType() {
        currentTaskType = when ((currentQuestion - 1) / trialsPerTask) {
            0 -> AttentionTaskType.SIMPLE_CPT
            1 -> AttentionTaskType.SELECTIVE_ATTENTION
            2 -> AttentionTaskType.VIGILANCE_TASK
            3 -> AttentionTaskType.DUAL_TASK
            else -> AttentionTaskType.SIMPLE_CPT
        }
        
        // Reset trial counter for new task type
        if ((currentQuestion - 1) % trialsPerTask == 0) {
            currentTrial = 0
            adjustTaskParameters()
        }
    }
    
    private fun adjustTaskParameters() {
        when (currentTaskType) {
            AttentionTaskType.SIMPLE_CPT -> {
                stimulusDisplayTime = 500L
                interTrialInterval = 1500L
                targetStimulus = "X"
            }
            AttentionTaskType.SELECTIVE_ATTENTION -> {
                stimulusDisplayTime = 750L
                interTrialInterval = 1200L
                targetStimulus = "X"
            }
            AttentionTaskType.VIGILANCE_TASK -> {
                stimulusDisplayTime = 300L
                interTrialInterval = 2000L
                targetStimulus = "Z" // Rare target
            }
            AttentionTaskType.DUAL_TASK -> {
                stimulusDisplayTime = 600L
                interTrialInterval = 1000L
                targetStimulus = "X"
            }
        }
    }
    
    private fun startTrialSequence() {
        // Prepare trial
        generateTrial()
        
        // Show fixation point
        showFixationPoint()
        
        // Start trial after brief delay
        Handler(Looper.getMainLooper()).postDelayed({
            showStimulus()
        }, 500)
    }
    
    private fun generateTrial() {
        // Determine if this is a target trial
        isTargetTrial = when (currentTaskType) {
            AttentionTaskType.VIGILANCE_TASK -> Random.nextFloat() < 0.15f // 15% targets for vigilance
            else -> Random.nextFloat() < 0.3f // 30% targets for other tasks
        }
        
        // Select stimulus
        currentStimulus = if (isTargetTrial) {
            targetStimulus
        } else {
            letters.filter { it != targetStimulus }.random()
        }
        
        // Setup distractors for selective attention and dual task
        setupTrialDistractors()
    }
    
    private fun setupTrialDistractors() {
        val showDistractors = currentTaskType == AttentionTaskType.SELECTIVE_ATTENTION || 
                             currentTaskType == AttentionTaskType.DUAL_TASK
        
        binding.apply {
            val distractors = listOf(distractor1, distractor2, distractor3, distractor4)
            
            if (showDistractors) {
                distractors.forEachIndexed { _, distractor ->
                    distractor.visibility = View.VISIBLE
                    distractor.text = shapes.random()
                    distractor.setTextColor(ContextCompat.getColor(this@AttentionTestActivity, colors.random()))
                    
                    // Animate distractors for dual task
                    if (currentTaskType == AttentionTaskType.DUAL_TASK) {
                        animateDistractor(distractor)
                    }
                }
            } else {
                distractors.forEach { it.visibility = View.GONE }
            }
        }
    }
    
    private fun animateDistractor(distractor: View) {
        distractor.animate()
            .rotationBy(360f)
            .setDuration(2000)
            .start()
    }
    
    private fun showFixationPoint() {
        binding.apply {
            stimulusDisplay.text = "+"
            stimulusDisplay.visibility = View.VISIBLE
            stimulusDisplay.setTextColor(ContextCompat.getColor(this@AttentionTestActivity, R.color.text_secondary))
            responseButton.isEnabled = false
        }
    }
    
    private fun showStimulus() {
        trialStartTime = System.currentTimeMillis()
        
        binding.apply {
            stimulusDisplay.text = currentStimulus
            stimulusDisplay.setTextColor(ContextCompat.getColor(this@AttentionTestActivity, R.color.text_primary))
            responseButton.isEnabled = true
            
            // Add color variation for some tasks
            if (currentTaskType == AttentionTaskType.SELECTIVE_ATTENTION) {
                stimulusDisplay.setTextColor(ContextCompat.getColor(this@AttentionTestActivity, colors.random()))
            }
        }
        
        // Hide stimulus after display time
        Handler(Looper.getMainLooper()).postDelayed({
            hideStimulus()
        }, stimulusDisplayTime)
    }
    
    private fun hideStimulus() {
        binding.stimulusDisplay.visibility = View.INVISIBLE
        
        // Wait for response or timeout
        Handler(Looper.getMainLooper()).postDelayed({
            if (binding.responseButton.isEnabled) {
                // No response - record miss or correct rejection
                onTrialTimeout()
            }
        }, interTrialInterval - stimulusDisplayTime)
    }
    
    private fun onResponseButtonClicked() {
        if (!binding.responseButton.isEnabled) return
        
        val reactionTime = System.currentTimeMillis() - trialStartTime
        binding.responseButton.isEnabled = false
        
        // Record response
        if (isTargetTrial) {
            hits++
            reactionTimes.add(reactionTime)
            showFeedback("Hit!", R.color.success_green)
        } else {
            falseAlarms++
            showFeedback("False Alarm", R.color.error_red)
        }
        
        // Continue to next trial
        Handler(Looper.getMainLooper()).postDelayed({
            nextTrial()
        }, 800)
    }
    
    private fun onTrialTimeout() {
        binding.responseButton.isEnabled = false
        
        // Record non-response
        if (isTargetTrial) {
            misses++
            showFeedback("Miss", R.color.warning_orange)
        } else {
            correctRejections++
            // No feedback for correct rejections to avoid disrupting flow
        }
        
        // Continue to next trial
        Handler(Looper.getMainLooper()).postDelayed({
            nextTrial()
        }, if (isTargetTrial) 800 else 200)
    }
    
    private fun showFeedback(message: String, colorRes: Int) {
        binding.apply {
            feedbackText.text = message
            feedbackText.setTextColor(ContextCompat.getColor(this@AttentionTestActivity, colorRes))
            feedbackText.visibility = View.VISIBLE
            
            Handler(Looper.getMainLooper()).postDelayed({
                feedbackText.visibility = View.GONE
            }, 600)
        }
    }
    
    private fun nextTrial() {
        currentTrial++
        
        if (currentTrial >= trialsPerTask) {
            // Task complete, update scores
            updateTaskScores()
            
            // Move to next question/task
            val isCorrect = calculateTrialAccuracy() > 0.7 // 70% accuracy threshold
            completeQuestion(isCorrect = isCorrect)
        } else {
            // Continue with next trial
            startTrialSequence()
        }
    }
    
    private fun updateTaskScores() {
        val accuracy = calculateTrialAccuracy()
        val avgRT = if (reactionTimes.isNotEmpty()) reactionTimes.average() else 1000.0
        
        // Combine accuracy and reaction time for comprehensive scoring
        val speedBonus = if (avgRT < 500) 10 else if (avgRT < 800) 5 else 0
        val combinedScore = (accuracy + speedBonus).coerceAtMost(100.0)

        when (currentTaskType) {
            AttentionTaskType.SIMPLE_CPT -> sustainedAttentionScore = combinedScore
            AttentionTaskType.SELECTIVE_ATTENTION -> selectiveAttentionScore = combinedScore
            AttentionTaskType.VIGILANCE_TASK -> vigilanceScore = combinedScore
            AttentionTaskType.DUAL_TASK -> distractorResistanceScore = combinedScore
        }
        
        // Reset counters for next task
        hits = 0
        misses = 0
        falseAlarms = 0
        correctRejections = 0
        reactionTimes.clear()
    }
    
    private fun calculateTrialAccuracy(): Double {
        val totalTrials = hits + misses + falseAlarms + correctRejections
        val correctResponses = hits + correctRejections
        return if (totalTrials > 0) correctResponses.toDouble() / totalTrials else 0.0
    }
    
    private fun getTaskTypeDescription(taskType: AttentionTaskType): String {
        return when (taskType) {
            AttentionTaskType.SIMPLE_CPT -> "Sustained Attention"
            AttentionTaskType.SELECTIVE_ATTENTION -> "Selective Attention"
            AttentionTaskType.VIGILANCE_TASK -> "Vigilance Task"
            AttentionTaskType.DUAL_TASK -> "Divided Attention"
        }
    }
    
    override fun getTestType(): TestType = TestType.COGNITIVE
    
    override fun calculateScore(): Int {
        val totalTasks = questionResults.size
        val correctTasks = questionResults.count { it.isCorrect == true }
        return if (totalTasks > 0) (correctTasks * 100) / totalTasks else 0
    }
    
    override fun generateDetailedScores(): Map<String, Double> {
        return mapOf(
            "Sustained Attention" to (sustainedAttentionScore * 100),
            "Selective Attention" to (selectiveAttentionScore * 100),
            "Vigilance" to (vigilanceScore * 100),
            "Distractor Resistance" to (distractorResistanceScore * 100)
        )
    }
    
    override fun generateInsights(): List<String> {
        val insights = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        
        detailedScores.forEach { (type, score) ->
            when {
                score >= 80 -> insights.add("Strong $type - you maintain focus well in this area")
                score >= 60 -> insights.add("Moderate $type - room for improvement with practice")
                else -> insights.add("$type challenges detected - consider attention training exercises")
            }
        }
        
        val avgRT = if (reactionTimes.isNotEmpty()) reactionTimes.average() else 0.0
        when {
            avgRT < 400 -> insights.add("Very fast response times - excellent alertness")
            avgRT < 600 -> insights.add("Good response speed - well-maintained attention")
            else -> insights.add("Slower response times may indicate attention fatigue")
        }
        
        return insights
    }
    
    override fun generateRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        
        if (detailedScores["Sustained Attention"]!! < 70) {
            recommendations.add("Practice meditation and mindfulness to improve sustained attention")
        }
        
        if (detailedScores["Selective Attention"]!! < 70) {
            recommendations.add("Try selective attention exercises with background distractors")
        }
        
        if (detailedScores["Vigilance"]!! < 70) {
            recommendations.add("Practice vigilance tasks with rare target detection")
        }
        
        recommendations.add("Regular breaks during focused work help maintain attention")
        recommendations.add("Reduce environmental distractions when concentration is needed")
        recommendations.add("Physical exercise improves attention and cognitive control")
        
        return recommendations
    }
    
    override fun showInstructions() {
        binding.instructionText.text = "Press the button when you see the target letter. Stay focused throughout the test."
    }
}
