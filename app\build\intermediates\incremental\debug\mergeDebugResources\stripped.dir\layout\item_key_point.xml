<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingVertical="6dp">

    <!-- Bullet Point -->
    <View
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/circle_primary"
        android:layout_marginTop="2dp" />

    <!-- Key Point Text -->
    <TextView
        android:id="@+id/key_point_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Key point text"
        android:textSize="14sp"
        android:textColor="@color/text_secondary"
        android:lineSpacingMultiplier="1.1" />

</LinearLayout>
