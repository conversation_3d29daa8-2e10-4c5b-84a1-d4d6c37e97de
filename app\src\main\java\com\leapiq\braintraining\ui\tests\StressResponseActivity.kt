package com.leapiq.braintraining.ui.tests

import android.os.Bundle
import android.view.View
import android.widget.RadioButton
import android.widget.RadioGroup
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityTestQuestionBaseBinding
import com.leapiq.braintraining.data.model.TestType

// Stress response categories
enum class StressResponseType {
    RESILIENCE,          // How well you bounce back from stress
    COPING_STRATEGIES,   // Methods used to handle stress
    PRESSURE_HANDLING,   // Performance under pressure
    EMOTIONAL_REGULATION // Managing emotions during stress
}

// Question data classes
data class StressResponseQuestion(
    val id: Int,
    val scenario: String,
    val questionText: String,
    val options: List<StressResponseOption>,
    val responseType: StressResponseType,
    val isTimedQuestion: Boolean = false,
    val timeLimitSeconds: Int = 30
)

data class StressResponseOption(
    val text: String,
    val score: Int, // 1-5 scale (1 = poor stress response, 5 = excellent stress response)
    val responseType: StressResponseType,
    val isSelected: Boolean = false
)

/**
 * Stress Response Test - Evaluates how users handle pressure situations
 * Tests stress resilience, coping strategies, and pressure handling
 * Dynamic timing based on response confidence and question complexity
 */
class StressResponseActivity : BaseTestActivity() {

    private lateinit var binding: ActivityTestQuestionBaseBinding

    // Test configuration
    private var currentQuestionIndex = 0
    private val questions = mutableListOf<StressResponseQuestion>()

    // Scoring data
    private var stressResilienceScore = 0
    private var copingStrategiesScore = 0
    private var pressureHandlingScore = 0
    private var emotionalRegulationScore = 0
    

    
    override fun initializeUI() {
        binding = ActivityTestQuestionBaseBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupUI()
    }

    override fun initializeTest() {
        generateQuestions()
        totalQuestions = questions.size
        // Load first question
        if (questions.isNotEmpty()) {
            currentQuestion = 1
            loadQuestion(1)
        }
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            toolbar.title = "Stress Response Test"

            // Setup navigation buttons
            btnPrevious.setOnClickListener { goToPreviousQuestion() }
            btnNext.setOnClickListener { goToNextQuestion() }
            btnSubmit.setOnClickListener { submitCurrentQuestion() }
            
            // Setup timer
            timerContainer.visibility = View.GONE
        }
    }
    
    private fun setupQuestionAdapter() {
        questionAdapter = StressResponseQuestionAdapter { optionIndex ->
            onOptionSelected(optionIndex)
        }
        
        binding.optionsRecycler.apply {
            layoutManager = LinearLayoutManager(this@StressResponseActivity)
            adapter = questionAdapter
        }
    }
    
    private fun generateQuestions() {
        questions.addAll(listOf(
            // Stress Resilience Questions
            StressResponseQuestion(
                1,
                "You've been working on an important project for weeks, and your computer crashes, losing all your work.",
                "How do you typically respond to this situation?",
                listOf(
                    StressResponseOption("I panic and feel overwhelmed, unable to think clearly", 1, StressResponseType.RESILIENCE),
                    StressResponseOption("I get frustrated but try to remember what I can and start over", 3, StressResponseType.RESILIENCE),
                    StressResponseOption("I take a deep breath, assess what can be recovered, and make a plan", 5, StressResponseType.RESILIENCE),
                    StressResponseOption("I blame myself and feel like giving up", 1, StressResponseType.RESILIENCE)
                ),
                StressResponseType.RESILIENCE
            ),
            
            StressResponseQuestion(
                2,
                "You're in a job interview for your dream position, and the interviewer asks a question you weren't prepared for.",
                "What's your immediate response?",
                listOf(
                    StressResponseOption("I freeze up and struggle to say anything coherent", 1, StressResponseType.PRESSURE_HANDLING),
                    StressResponseOption("I admit I don't know but explain how I would find the answer", 4, StressResponseType.PRESSURE_HANDLING),
                    StressResponseOption("I stay calm and relate it to my relevant experience", 5, StressResponseType.PRESSURE_HANDLING),
                    StressResponseOption("I get nervous and start rambling about unrelated topics", 2, StressResponseType.PRESSURE_HANDLING)
                ),
                StressResponseType.PRESSURE_HANDLING,
                isTimedQuestion = true,
                timeLimitSeconds = 20
            ),
            
            StressResponseQuestion(
                3,
                "You're dealing with multiple deadlines at work, and your manager adds another urgent task to your plate.",
                "How do you handle this additional pressure?",
                listOf(
                    StressResponseOption("I prioritize tasks and communicate realistic timelines", 5, StressResponseType.COPING_STRATEGIES),
                    StressResponseOption("I try to do everything at once and end up stressed", 2, StressResponseType.COPING_STRATEGIES),
                    StressResponseOption("I work longer hours without considering my well-being", 2, StressResponseType.COPING_STRATEGIES),
                    StressResponseOption("I delegate what I can and focus on high-priority items", 4, StressResponseType.COPING_STRATEGIES)
                ),
                StressResponseType.COPING_STRATEGIES
            ),
            
            StressResponseQuestion(
                4,
                "During a heated argument with a close friend, you feel your emotions rising.",
                "How do you manage your emotional response?",
                listOf(
                    StressResponseOption("I take a moment to breathe and think before responding", 5, StressResponseType.EMOTIONAL_REGULATION),
                    StressResponseOption("I react immediately based on how I'm feeling", 1, StressResponseType.EMOTIONAL_REGULATION),
                    StressResponseOption("I try to stay calm but sometimes lose control", 3, StressResponseType.EMOTIONAL_REGULATION),
                    StressResponseOption("I walk away to cool down before continuing", 4, StressResponseType.EMOTIONAL_REGULATION)
                ),
                StressResponseType.EMOTIONAL_REGULATION
            ),
            
            StressResponseQuestion(
                5,
                "You're giving a presentation to senior executives, and you notice you're making mistakes.",
                "What's your strategy for handling this pressure?",
                listOf(
                    StressResponseOption("I acknowledge the mistake briefly and continue confidently", 5, StressResponseType.PRESSURE_HANDLING),
                    StressResponseOption("I get flustered and the mistakes multiply", 1, StressResponseType.PRESSURE_HANDLING),
                    StressResponseOption("I pause, collect myself, and refocus on my key points", 4, StressResponseType.PRESSURE_HANDLING),
                    StressResponseOption("I rush through the rest to finish quickly", 2, StressResponseType.PRESSURE_HANDLING)
                ),
                StressResponseType.PRESSURE_HANDLING,
                isTimedQuestion = true,
                timeLimitSeconds = 25
            ),
            
            StressResponseQuestion(
                6,
                "You've experienced a significant personal setback (job loss, relationship end, health issue).",
                "How do you typically cope with major life stressors?",
                listOf(
                    StressResponseOption("I seek support from friends, family, or professionals", 5, StressResponseType.COPING_STRATEGIES),
                    StressResponseOption("I isolate myself and try to handle it alone", 2, StressResponseType.COPING_STRATEGIES),
                    StressResponseOption("I use healthy activities like exercise or hobbies to cope", 4, StressResponseType.COPING_STRATEGIES),
                    StressResponseOption("I tend to avoid dealing with it and hope it resolves itself", 1, StressResponseType.COPING_STRATEGIES)
                ),
                StressResponseType.COPING_STRATEGIES
            ),
            
            StressResponseQuestion(
                7,
                "You're in a traffic jam and running late for an important meeting.",
                "How do you handle this stressful situation?",
                listOf(
                    StressResponseOption("I call ahead to explain and use the time productively", 5, StressResponseType.RESILIENCE),
                    StressResponseOption("I get increasingly agitated and honk at other drivers", 1, StressResponseType.RESILIENCE),
                    StressResponseOption("I accept the situation and try to stay calm", 4, StressResponseType.RESILIENCE),
                    StressResponseOption("I worry about the consequences and feel anxious", 2, StressResponseType.RESILIENCE)
                ),
                StressResponseType.RESILIENCE
            ),
            
            StressResponseQuestion(
                8,
                "You receive harsh criticism about your work from someone you respect.",
                "How do you emotionally process this feedback?",
                listOf(
                    StressResponseOption("I feel hurt initially but try to learn from the feedback", 4, StressResponseType.EMOTIONAL_REGULATION),
                    StressResponseOption("I take it personally and feel defensive", 2, StressResponseType.EMOTIONAL_REGULATION),
                    StressResponseOption("I stay objective and focus on areas for improvement", 5, StressResponseType.EMOTIONAL_REGULATION),
                    StressResponseOption("I dismiss the criticism and feel angry", 1, StressResponseType.EMOTIONAL_REGULATION)
                ),
                StressResponseType.EMOTIONAL_REGULATION
            ),
            
            StressResponseQuestion(
                9,
                "You're facing a tight deadline with limited resources and unclear requirements.",
                "What's your approach to managing this stressful work situation?",
                listOf(
                    StressResponseOption("I clarify requirements and create a realistic plan", 5, StressResponseType.PRESSURE_HANDLING),
                    StressResponseOption("I work frantically hoping everything will work out", 1, StressResponseType.PRESSURE_HANDLING),
                    StressResponseOption("I do my best with available information and adapt as needed", 4, StressResponseType.PRESSURE_HANDLING),
                    StressResponseOption("I stress about the uncertainty and struggle to start", 2, StressResponseType.PRESSURE_HANDLING)
                ),
                StressResponseType.PRESSURE_HANDLING,
                isTimedQuestion = true,
                timeLimitSeconds = 30
            ),
            
            StressResponseQuestion(
                10,
                "After a particularly stressful week, you notice you're feeling burned out.",
                "How do you typically recover from stress and prevent burnout?",
                listOf(
                    StressResponseOption("I maintain work-life balance and practice self-care regularly", 5, StressResponseType.COPING_STRATEGIES),
                    StressResponseOption("I push through and ignore the warning signs", 1, StressResponseType.COPING_STRATEGIES),
                    StressResponseOption("I take breaks when possible but often neglect self-care", 3, StressResponseType.COPING_STRATEGIES),
                    StressResponseOption("I recognize the signs and actively seek ways to recharge", 4, StressResponseType.COPING_STRATEGIES)
                ),
                StressResponseType.COPING_STRATEGIES
            ),
            
            StressResponseQuestion(
                11,
                "You're in a group project where team members aren't contributing equally, creating stress.",
                "How do you handle this interpersonal stress?",
                listOf(
                    StressResponseOption("I address the issue directly but diplomatically with the team", 5, StressResponseType.EMOTIONAL_REGULATION),
                    StressResponseOption("I do all the work myself to avoid conflict", 2, StressResponseType.EMOTIONAL_REGULATION),
                    StressResponseOption("I get frustrated but try to find a compromise", 3, StressResponseType.EMOTIONAL_REGULATION),
                    StressResponseOption("I complain to others but don't address it directly", 1, StressResponseType.EMOTIONAL_REGULATION)
                ),
                StressResponseType.EMOTIONAL_REGULATION
            ),
            
            StressResponseQuestion(
                12,
                "You're facing financial difficulties and need to make tough decisions quickly.",
                "How do you handle this high-stress situation?",
                listOf(
                    StressResponseOption("I analyze options systematically and seek advice if needed", 5, StressResponseType.RESILIENCE),
                    StressResponseOption("I panic and make impulsive decisions", 1, StressResponseType.RESILIENCE),
                    StressResponseOption("I try to stay calm and focus on what I can control", 4, StressResponseType.RESILIENCE),
                    StressResponseOption("I worry constantly but struggle to take action", 2, StressResponseType.RESILIENCE)
                ),
                StressResponseType.RESILIENCE,
                isTimedQuestion = true,
                timeLimitSeconds = 35
            )
        ))
    }
    
    override fun startQuestion() {
        updateProgress()
        displayCurrentQuestion()
        startQuestionTimer()
    }
    
    private fun updateProgress() {
        binding.apply {
            progressBar.progress = currentQuestion - 1
            progressText.text = "Question ${currentQuestion} of ${totalQuestions}"
            questionNumberText.text = "Question ${currentQuestion}"
        }
    }
    
    private fun displayCurrentQuestion() {
        if (currentQuestionIndex < questions.size) {
            val question = questions[currentQuestionIndex]
            
            binding.apply {
                scenarioText.text = question.scenario
                questionText.text = question.questionText
                questionAdapter.updateQuestion(question)
                
                // Show/hide timer based on question type
                isTimedQuestion = question.isTimedQuestion
                if (isTimedQuestion) {
                    timeLimit = question.timeLimitSeconds * 1000L
                    timerContainer.visibility = View.VISIBLE
                    timerText.text = "${question.timeLimitSeconds}s"
                } else {
                    timerContainer.visibility = View.GONE
                }
                
                // Update navigation buttons
                btnNext.visibility = if (hasSelectedOption()) View.VISIBLE else View.GONE
                btnSubmit.visibility = if (hasSelectedOption()) View.VISIBLE else View.GONE
            }
        }
    }
    
    private fun startQuestionTimer() {
        if (isTimedQuestion && timeLimit > 0) {
            val startTime = System.currentTimeMillis()
            val handler = Handler(Looper.getMainLooper())
            
            val updateTimer = object : Runnable {
                override fun run() {
                    val elapsed = System.currentTimeMillis() - startTime
                    val remaining = maxOf(0, timeLimit - elapsed)
                    val secondsRemaining = (remaining / 1000).toInt()
                    
                    binding.timerText.text = "${secondsRemaining}s"
                    
                    // Change color as time runs out
                    val color = when {
                        secondsRemaining <= 5 -> android.R.color.holo_red_dark
                        secondsRemaining <= 10 -> android.R.color.holo_orange_dark
                        else -> android.R.color.holo_blue_dark
                    }
                    binding.timerText.setTextColor(resources.getColor(color, null))
                    
                    if (remaining > 0) {
                        handler.postDelayed(this, 100)
                    } else {
                        // Time's up - auto-submit if option selected, or mark as timeout
                        onTimeUp()
                    }
                }
            }
            
            handler.post(updateTimer)
        }
    }
    
    private fun onTimeUp() {
        if (hasSelectedOption()) {
            nextQuestion()
        } else {
            // No selection made - record timeout and move to next question
            recordCurrentResponse(isTimeout = true)
            if (currentQuestionIndex < questions.size - 1) {
                currentQuestionIndex++
                currentQuestion++
                displayCurrentQuestion()
                startQuestionTimer()
            } else {
                completeQuestion(isCorrect = true)
            }
        }
    }
    
    private fun onOptionSelected(optionIndex: Int) {
        if (currentQuestionIndex < questions.size) {
            val question = questions[currentQuestionIndex]
            
            // Clear previous selections and set new selection
            val updatedOptions = question.options.mapIndexed { index, option ->
                option.copy(isSelected = index == optionIndex)
            }
            questions[currentQuestionIndex] = question.copy(options = updatedOptions)
            
            // Update UI
            questionAdapter.notifyDataSetChanged()
            binding.apply {
                btnNext.visibility = View.VISIBLE
                btnSubmit.visibility = View.VISIBLE
            }
        }
    }
    
    private fun hasSelectedOption(): Boolean {
        return if (currentQuestionIndex < questions.size) {
            questions[currentQuestionIndex].options.any { it.isSelected }
        } else false
    }
    
    private fun nextQuestion() {
        if (hasSelectedOption()) {
            recordCurrentResponse()
            
            if (currentQuestionIndex < questions.size - 1) {
                currentQuestionIndex++
                currentQuestion++
                displayCurrentQuestion()
                startQuestionTimer()
            } else {
                // Test complete
                completeQuestion(isCorrect = true)
            }
        }
    }
    
    private fun submitCurrentQuestion() {
        nextQuestion()
    }
    
    private fun recordCurrentResponse(isTimeout: Boolean = false) {
        if (currentQuestionIndex < questions.size) {
            val question = questions[currentQuestionIndex]
            val selectedOption = question.options.find { it.isSelected }
            
            if (selectedOption != null) {
                // Update scores based on selected option
                when (selectedOption.responseType) {
                    StressResponseType.RESILIENCE -> stressResilienceScore += selectedOption.score
                    StressResponseType.COPING_STRATEGIES -> copingStrategiesScore += selectedOption.score
                    StressResponseType.PRESSURE_HANDLING -> pressureHandlingScore += selectedOption.score
                    StressResponseType.EMOTIONAL_REGULATION -> emotionalRegulationScore += selectedOption.score
                }
            }
            
            // Record the response
            val responseTime = System.currentTimeMillis() - questionStartTime
            val selectedAnswer = if (isTimeout) "TIMEOUT" else (selectedOption?.text ?: "NO_SELECTION")
            
            completeQuestion(
                isCorrect = null, // No correct/incorrect for personality tests
                selectedAnswer = selectedAnswer,
                responseTime = responseTime
            )
        }
    }
    
    override fun getTestType(): TestType = TestType.PERSONALITY
    
    override fun calculateScore(): Int {
        // Calculate overall stress response score (0-100)
        val totalQuestions = questions.size
        val maxPossibleScore = totalQuestions * 5 // Maximum score per question is 5
        val actualScore = stressResilienceScore + copingStrategiesScore + pressureHandlingScore + emotionalRegulationScore
        
        return if (maxPossibleScore > 0) (actualScore * 100) / maxPossibleScore else 0
    }
    
    override fun generateDetailedScores(): Map<String, Double> {
        val resilienceQuestions = questions.count { it.responseType == StressResponseType.RESILIENCE }
        val copingQuestions = questions.count { it.responseType == StressResponseType.COPING_STRATEGIES }
        val pressureQuestions = questions.count { it.responseType == StressResponseType.PRESSURE_HANDLING }
        val emotionalQuestions = questions.count { it.responseType == StressResponseType.EMOTIONAL_REGULATION }
        
        return mapOf(
            "Stress Resilience" to if (resilienceQuestions > 0) (stressResilienceScore.toDouble() / (resilienceQuestions * 5) * 100) else 0.0,
            "Coping Strategies" to if (copingQuestions > 0) (copingStrategiesScore.toDouble() / (copingQuestions * 5) * 100) else 0.0,
            "Pressure Handling" to if (pressureQuestions > 0) (pressureHandlingScore.toDouble() / (pressureQuestions * 5) * 100) else 0.0,
            "Emotional Regulation" to if (emotionalQuestions > 0) (emotionalRegulationScore.toDouble() / (emotionalQuestions * 5) * 100) else 0.0
        )
    }
    
    override fun generateInsights(): List<String> {
        val insights = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        val overallScore = calculateScore()
        
        // Overall stress response assessment
        when {
            overallScore >= 80 -> insights.add("Excellent stress management - you handle pressure situations very effectively")
            overallScore >= 60 -> insights.add("Good stress management - you cope well with most stressful situations")
            overallScore >= 40 -> insights.add("Moderate stress management - there's room for improvement in handling pressure")
            else -> insights.add("Stress management challenges - consider developing better coping strategies")
        }
        
        // Specific area insights
        detailedScores.forEach { (area, score) ->
            when {
                score >= 80 -> insights.add("Strong $area - this is one of your stress management strengths")
                score < 50 -> insights.add("$area could benefit from focused improvement and practice")
            }
        }
        
        return insights
    }
    
    override fun generateRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        
        if (detailedScores["Stress Resilience"]!! < 60) {
            recommendations.add("Practice mindfulness and develop a growth mindset to build resilience")
            recommendations.add("Build a strong support network of friends, family, and mentors")
        }
        
        if (detailedScores["Coping Strategies"]!! < 60) {
            recommendations.add("Learn healthy coping mechanisms like exercise, meditation, or creative outlets")
            recommendations.add("Develop time management and prioritization skills")
        }
        
        if (detailedScores["Pressure Handling"]!! < 60) {
            recommendations.add("Practice pressure situations in low-stakes environments")
            recommendations.add("Develop preparation strategies and contingency planning")
        }
        
        if (detailedScores["Emotional Regulation"]!! < 60) {
            recommendations.add("Learn emotional regulation techniques like deep breathing and cognitive reframing")
            recommendations.add("Consider mindfulness or emotional intelligence training")
        }
        
        recommendations.add("Regular exercise and adequate sleep significantly improve stress resilience")
        recommendations.add("Consider professional support if stress significantly impacts your daily life")
        
        return recommendations
    }
    
    override fun showInstructions() {
        binding.instructionText.text = "Read each scenario carefully and select the response that best describes how you would typically handle the situation. Some questions are timed to simulate pressure."
    }

    override fun loadQuestion(questionNumber: Int) {
        if (questionNumber <= questions.size) {
            currentQuestionIndex = questionNumber - 1
            val question = questions[currentQuestionIndex]

            binding.apply {
                // Update progress
                progressBar.progress = questionNumber
                progressText.text = "Question $questionNumber of ${questions.size}"

                // Set question text (clean, no labels)
                questionText.text = question.scenario

                // Clear previous content
                questionContent.removeAllViews()

                // Create radio group for options
                val radioGroup = RadioGroup(this@StressResponseActivity).apply {
                    orientation = RadioGroup.VERTICAL
                    setPadding(0, 16, 0, 16)
                }

                // Add radio buttons for each option
                question.options.forEachIndexed { index, option ->
                    val radioButton = RadioButton(this@StressResponseActivity).apply {
                        text = option.text
                        textSize = 16f
                        setTextColor(ContextCompat.getColor(context, R.color.text_primary))
                        setPadding(16, 12, 16, 12)
                        id = index
                    }
                    radioGroup.addView(radioButton)
                }

                // Restore saved answer if exists
                val savedAnswer = getSavedAnswer() as? Int
                if (savedAnswer != null && savedAnswer < radioGroup.childCount) {
                    (radioGroup.getChildAt(savedAnswer) as RadioButton).isChecked = true
                }

                // Set selection listener
                radioGroup.setOnCheckedChangeListener { _, checkedId ->
                    if (checkedId != -1) {
                        saveCurrentAnswer(checkedId)
                        updateNavigationButtons()
                    }
                }

                questionContent.addView(radioGroup)
                updateNavigationButtons()
            }
        }
    }

    override fun updateNavigationButtons() {
        binding.apply {
            // Previous button visibility
            btnPrevious.visibility = if (currentQuestion > 1) View.VISIBLE else View.GONE

            // Next/Finish button
            if (currentQuestion == totalQuestions) {
                btnNext.text = "Finish"
                btnNext.setOnClickListener { finishTest() }
            } else {
                btnNext.text = "Next"
                btnNext.setOnClickListener { goToNextQuestion() }
            }

            // Enable/disable based on answer
            val hasAnswer = getSavedAnswer() != null
            btnNext.isEnabled = hasAnswer
            btnNext.alpha = if (hasAnswer) 1.0f else 0.5f
        }
    }

    private fun finishTest() {
        // Calculate scores from all answers
        calculateFinalScores()
        completeTest()
    }

    private fun calculateFinalScores() {
        // Reset scores
        resilienceScore = 0
        copingScore = 0
        pressureScore = 0
        emotionalScore = 0

        // Calculate scores based on user answers
        userAnswers.forEach { (questionIndex, answerIndex) ->
            if (questionIndex < questions.size && answerIndex is Int) {
                val question = questions[questionIndex]
                if (answerIndex < question.options.size) {
                    val selectedOption = question.options[answerIndex]
                    when (selectedOption.responseType) {
                        StressResponseType.RESILIENCE -> resilienceScore += selectedOption.score
                        StressResponseType.COPING_STRATEGIES -> copingScore += selectedOption.score
                        StressResponseType.PRESSURE_HANDLING -> pressureScore += selectedOption.score
                        StressResponseType.EMOTIONAL_REGULATION -> emotionalScore += selectedOption.score
                    }
                }
            }
        }
    }
}
